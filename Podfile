source 'https://github.com/CocoaPods/Specs.git'
source "https://git.haier.net/uplus/shell/cocoapods/Specs.git"
 
platform :ios, '10.0'

def project_pods
  pod 'UpPluginFoundation', '0.1.16.1.2023020301'
  pod 'UPPluginBaseAPI', '0.1.3'
  pod 'uplog','1.4.0'
  pod 'upnetwork','4.0.6'
  pod 'UPResource','2.19.1.1.2022041402'
  pod 'UPVDN','2.7.1.1.2023032102'
  pod 'UPCore/UPContext','3.5.9.2023120401'
  pod 'UPCore/toggles','3.5.9.2023120401',:modular_headers => true
  pod 'UPCache','1.1.0.2238.2022101401'
  pod 'upuserdomain','3.20.0.2023101201'
  pod 'UPStorage','1.4.15'
       target "UpAppinfoPluginTests" do
          inherit! :search_paths
          pod 'Cucumberish','1.4.0'
          pod 'OCMock','3.8.1'
          pod 'OCHamcrest', '~> 7.1.2'
      end
      
      target "UpAppinfoPluginFramework" do
          inherit! :search_paths
#          use_frameworks! :linkage => :static
      end
       
      target "UpAppinfoPluginDebuger" do
          inherit! :search_paths
      end
end

# Comment the next line if you don't want to use dynamic frameworks
# use_frameworks!
target 'UpAppinfoPlugin' do
  project_pods
end

