Feature: 获取灰度状态

    该功能是指获取当前的灰度状态,无需任何参数，获取到灰度状态后直接返回
    依赖：UpBase库

    Background:
      Given UpAppInfoPluginManager已经初始化

    Scenario Outline: [30000]预期获取设备灰度状态成功
      Given 创建基于"<platform>"平台的"isGrayModeForAppInfo"action
      Given 执行获取灰度模式成功返回的数据grayMode"<targetGrayStatus>"
      When 调用名称为"isGrayModeForAppInfo"的action,入参为"null"
      Then 执行名称为"isGrayModeForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"<retData>"

      Examples:
        | platform | targetGrayStatus       | retData |
        | Flutter  | false                  | false   |
        | Flutter  | true                   | true    |
        | Nebula   | false                  | false   |
        | Nebula   | true                   | true    |
