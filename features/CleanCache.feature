Feature: 清除缓存

    该功能是指清除缓存
    不需要参数
    逻辑:调用UpCache库的清除缓存接口后，返回成功
    依赖:UpCache库

    Background:
      Given UpAppInfoPluginManager已经初始化

  @android_ignore
  Scenario Outline: [60000]预期清除缓存成功
    Given 创建基于"<platform>"平台的"cleanCacheForAppInfo"action
    When 调用名称为"cleanCacheForAppInfo"的action,入参为"null"
    Then 执行名称为"cleanCacheForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"null"
    Examples:
      | platform |
      | Flutter  |

  @ios_ignore
  Scenario Outline: [60000]预期清除缓存成功
    Given 创建基于"<platform>"平台的"cleanCacheForAppInfo"action
    Given 调用"cleanApplicationCache"方法时捕获callback
    When 调用名称为"cleanCacheForAppInfo"的action
    When 清除缓存结果回调
    Then 执行名称为"cleanCacheForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"null"

    Examples:
      | platform |
      | Flutter  |

