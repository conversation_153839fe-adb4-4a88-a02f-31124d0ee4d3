Feature: 获取App信息

    本功能是指获取App信息，无需任何参数。获取到App信息后会直接返回
    依赖：UpBase库

    Background:
      Given UpAppInfoPluginManager已经初始化

    @android_ignore
    Scenario Outline: [20000]预期获取App信息成功
      Given 创建基于"<Platform>"平台的"getAppInfoForAppInfo"action
      Given 执行获取Appinfo成功返回的数据为
        |appId   |appKey  |appVersion  |clientId  |haierClientId  |haierClientSecret  |haierUserCenterUrl  |grayMode  |env  | envPreSet  |idfa  |OSversion  |platform  |testMode  |
        |<appId> |<appKey>|<appVersion>|<clientId>|<haierClientId>|<haierClientSecret>|<haierUserCenterUrl>|<grayMode>|<env>| <envPreSet>|<idfa>|<OSversion>|<platform>|<testMode>|
      When 调用名称为"getAppInfoForAppInfo"的action,入参为"null"
      Then 执行名称为"getAppInfoForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:
        |appId   |appKey  |appVersion  |clientId  |haierClientId  |haierClientSecret  |haierUserCenterUrl  |grayMode  |env  |isPreViewEnv  |idfa  |OSversion  |platform  |testMode  |
        |<appId> |<appKey>|<appVersion>|<clientId>|<haierClientId>|<haierClientSecret>|<haierUserCenterUrl>|<grayMode>|<env>|<isPreViewEnv>|<idfa>|<OSversion>|<platform>|<testMode>|
      Examples:
        | Platform | env  |envPreSet|isPreViewEnv |appKey|appVersion|clientId|haierClientId|haierClientSecret|haierUserCenterUrl|grayMode|appId|idfa|OSversion|platform|testMode|
        | Flutter  | 验收  |1        |true         |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |  A  |   A     |    A   | true   |
        | Flutter  | 验证  |0        |false        |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |  A  |   A     |    A   | true   |
        | Flutter  | 生产  |0        |false        |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |  A  |   A     |    A   | true   |
        | Flutter  | 联调  |0        |false        |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |  A  |   A     |    A   | true   |
        | Nebula   | 验收  |1        |true         |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |  A  |   A     |    A   | true   |
        | Nebula   | 验证  |0        |false        |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |  A  |   A     |    A   | true   |
        | Nebula   | 生产  |0        |false        |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |  A  |   A     |    A   | true   |
        | Nebula   | 联调  |0        |false        |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |  A  |   A     |    A   | true   |

  @ios_ignore
  Scenario Outline: [20000-1]预期获取App信息成功
    Given 创建基于"<Platform>"平台的"getAppInfoForAppInfo"action
    Given 执行获取Appinfo成功返回的数据为
      |appId   |appKey  |appVersion  |clientId  |haierClientId  |haierClientSecret  |haierUserCenterUrl  |grayMode  |env   |envPreSet    |OSversion     |testMode  | versionCode|
      |<appId> |<appKey>|<appVersion>|<clientId>|<haierClientId>|<haierClientSecret>|<haierUserCenterUrl>|<grayMode>|<env> |<envPreSet>  |<buildVersion>|<testMode>| <versionCode> |
    When 调用名称为"getAppInfoForAppInfo"的action,入参为"null"
    Then 执行名称为"getAppInfoForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:
      |appId   |appKey  |appVersion  |clientId  |haierClientId  |haierClientSecret  |haierUserCenterUrl  |grayMode  |env     |isPreViewEnv   |idfa  |OSversion     |platform  |testMode  |versionCode|
      |<appId> |<appKey>|<appVersion>|<clientId>|<haierClientId>|<haierClientSecret>|<haierUserCenterUrl>|<grayMode>|<envAct>|<isPreViewEnv> |<idfa>|<OSversion>   |<platform>|<testMode>|<versionCode> |
    Examples:
      | Platform | env      |envPreSet|isPreViewEnv|envAct | buildVersion| OSversion|appKey|appVersion|clientId|haierClientId|haierClientSecret|haierUserCenterUrl|grayMode|appId|idfa|platform |testMode|versionCode|
      | Flutter  | LIANTIAO |1        |true     |联调    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Flutter  | YANSHOU  |0        |false    |验收    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Flutter  | YANZHENG |0        |false    |验证    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Flutter  | SHENGCHAN|0        |false    |生产    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Flutter  | TIAOSHI  |0        |false    |生产    | 10.1        | 10.1.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Nebula   | LIANTIAO |1        |true     |联调    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Nebula   | YANSHOU  |0        |false    |验收    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Nebula   | YANZHENG |0        |false    |验证    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Nebula   | SHENGCHAN|0        |false    |生产    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |
      | Nebula   | TIAOSHI  |0        |false    |生产    | 10.1        | 10.1.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10         |

  @ios_ignore
  Scenario Outline: [20000-2]getPackageInfo方法抛异常，预期获取App信息成功，versionCode为0
    Given 创建基于"<Platform>"平台的"getAppInfoForAppInfo"action
    Given 调用"getPackageInfo"方法时抛异常
    Given 执行获取Appinfo成功返回的数据为
      |appId   |appKey  |appVersion  |clientId  |haierClientId  |haierClientSecret  |haierUserCenterUrl  |grayMode  |env   |envPreSet    |OSversion     |testMode  | versionCode|
      |<appId> |<appKey>|<appVersion>|<clientId>|<haierClientId>|<haierClientSecret>|<haierUserCenterUrl>|<grayMode>|<env> |<envPreSet>  |<buildVersion>|<testMode>| 10         |
    When 调用名称为"getAppInfoForAppInfo"的action,入参为"null"
    Then 执行名称为"getAppInfoForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:
      |appId   |appKey  |appVersion  |clientId  |haierClientId  |haierClientSecret  |haierUserCenterUrl  |grayMode  |env     |isPreViewEnv   |idfa  |OSversion     |platform  |testMode  |versionCode|
      |<appId> |<appKey>|<appVersion>|<clientId>|<haierClientId>|<haierClientSecret>|<haierUserCenterUrl>|<grayMode>|<envAct>|<isPreViewEnv> |<idfa>|<OSversion>   |<platform>|<testMode>|<versionCode> |
    Examples:
      | Platform | env      |envPreSet|isPreViewEnv|envAct | buildVersion| OSversion|appKey|appVersion|clientId|haierClientId|haierClientSecret|haierUserCenterUrl|grayMode|appId|idfa|platform |testMode|versionCode|
      | Flutter  | LIANTIAO |1        |true        |联调    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   0       |


  @ios_ignore
  Scenario Outline: [20000-3]getLongVersionCode方法抛异常，预期获取App信息成功，versionCode通过getVersionCode再次获取
    Given 创建基于"<Platform>"平台的"getAppInfoForAppInfo"action
    Given 调用"getLongVersionCode"方法时抛异常
    Given 执行获取Appinfo成功返回的数据为
      |appId   |appKey  |appVersion  |clientId  |haierClientId  |haierClientSecret  |haierUserCenterUrl  |grayMode  |env   |envPreSet    |OSversion     |testMode  | versionCode|
      |<appId> |<appKey>|<appVersion>|<clientId>|<haierClientId>|<haierClientSecret>|<haierUserCenterUrl>|<grayMode>|<env> |<envPreSet>  |<buildVersion>|<testMode>| <versionCode> |
    When 调用名称为"getAppInfoForAppInfo"的action,入参为"null"
    Then 执行名称为"getAppInfoForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:
      |appId   |appKey  |appVersion  |clientId  |haierClientId  |haierClientSecret  |haierUserCenterUrl  |grayMode  |env     |isPreViewEnv   |idfa  |OSversion     |platform  |testMode  |versionCode|
      |<appId> |<appKey>|<appVersion>|<clientId>|<haierClientId>|<haierClientSecret>|<haierUserCenterUrl>|<grayMode>|<envAct>|<isPreViewEnv> |<idfa>|<OSversion>   |<platform>|<testMode>|<versionCode> |
    Examples:
      | Platform | env      |envPreSet|isPreViewEnv|envAct | buildVersion| OSversion|appKey|appVersion|clientId|haierClientId|haierClientSecret|haierUserCenterUrl|grayMode|appId|idfa|platform |testMode|versionCode|
      | Flutter  | LIANTIAO |1        |true        |联调    | 10          | 10.0.0   |  A   |  A      |    A    |      A      |      A          |        A         | true   |  A |     |Android  | true   |   10      |
