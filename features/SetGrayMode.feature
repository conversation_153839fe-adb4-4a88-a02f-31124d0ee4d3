Feature: 更新灰度状态

    本功能是设置当前的灰度状态.该功能所需参数如下:
    grayMode - 新的灰度状态 - 必传 - boolean
    依赖：UpBase库

    Background:
      Given UpAppInfoPluginManager已经初始化

    Scenario Outline: [40000]入参为空时返回参数错误
      Given 创建基于"<platform>"平台的"setGrayModeForAppInfo"action
      When 调用名称为"setGrayModeForAppInfo"的action,入参为"<param>"
      Then 执行"setGrayModeForAppInfo"失败,失败码:"900003",失败信息:"参数错误(<param>)"
      Examples:
        | platform | param   |
        | Flutter  | {}      |
        | Flutter  | null    |
        | Nebula   | {}      |
        | Nebula   | null    |

  Scenario Outline: [40001]预期更新灰度状态成功
    Given 创建基于"<platform>"平台的"setGrayModeForAppInfo"action
    When 调用名称为"setGrayModeForAppInfo"的action,入参为"<param>"
    Then 执行名称为"setGrayModeForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"null"
    Examples:
      | platform | param                 |
      | Flutter  | {\"grayMode\":false}  |
      | Flutter  | {\"grayMode\":true}   |
      | Nebula   | {\"grayMode\":false}  |
      | Nebula   | {\"grayMode\":true}   |
