Feature: 获取缓存大小

  该功能是指获取缓存大小，不需要入参
  逻辑:调用UpCache库的获取缓存大小接口后，返回成功
  依赖:UpCache库

    Background:
      Given UpAppInfoPluginManager已经初始化

    Scenario Outline: [50000]预期获取缓存大小成功
      Given 创建基于"<platform>"平台的"getCacheSizeForAppInfo"action
      Given 执行获取缓存大小返回的数据"<retData>"
      When 调用名称为"getCacheSizeForAppInfo"的action,入参为"null"
      Then 执行名称为"getCacheSizeForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"<retData>"

      Examples:
        | platform | retData |
        | Flutter  | 1234567 |
