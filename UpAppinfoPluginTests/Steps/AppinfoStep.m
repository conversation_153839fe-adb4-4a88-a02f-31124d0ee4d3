//
//  AppinfoStep.m
//  UpAppinfoPluginTests
//
//  Created by yaosixu on 2021/10/18.
//

#import "AppinfoStep.h"
#import <Cucumberish/Cucumberish.h>
#import <OCMock/OCMock.h>
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpAppinfoPluginManager.h"
#import "UpAppinfoPluginIMP.h"
#import "UPUnitTestCallBackIMP.h"
#import "StepsUtils.h"

@interface AppinfoStep ()

@property (nonatomic, strong) UpPluginAction *action;
@property (nonatomic, strong) id executeResult;

@end

@implementation AppinfoStep

- (instancetype)init
{
    self = [super init];
    if (self) {
        [self defineStepsAndHooks];
    }
    return self;
}

- (void)defineStepsAndHooks
{
    Given(@"^UpAppInfoPluginManager已经初始化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [UpAppinfoPluginManager load];
      id imp = OCMClassMock([UpAppinfoPluginIMP class]);
      [UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate = imp;
    });

    Given(@"^创建基于\"(.*)\"平台的\"(.*)\"action$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSString *actionName = args[1];
      CallMethodPlatformType platformType = platformTypeWithString(args[0]);
      self.action = ({
          Class cls = [UpPluginActionManager.sharedInstance getActionCreatorWithName:actionName];
          UpPluginAction *action = [cls new];
          action.callerType = platformType;
          action;
      });
    });

    Given(@"^执行获取设备信息成功返回数据model\"(.*)\",dpi\"(.*)\",size\"(.*)\",safeArea\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceIdentifier]).ignoringNonObjectArgs andReturn:args[0]];
      NSDictionary *dic = jsonObjectFromEscapedString(args[1]);
      [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceDpi]).ignoringNonObjectArgs andReturn:dic];
      NSDictionary *sizeDic = jsonObjectFromEscapedString(args[2]);
      [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getPhoneScreenSize]).ignoringNonObjectArgs andReturn:sizeDic];
      NSDictionary *safeAreaDic = jsonObjectFromEscapedString(args[3]);
      [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getPhoneSafeArea]).ignoringNonObjectArgs andReturn:safeAreaDic];
    });

    Given(@"^执行获取灰度模式成功返回的数据grayMode\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      BOOL status = args[0].boolValue;
      [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate isGrayMode]) andReturnValue:@(status)];
    });

    Given(@"^执行获取缓存大小返回的数据\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      unsigned long long fileSize = args[0].longLongValue;
      [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getCacheFilesSize:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
        void (^callback)(unsigned long long);
        [invocation getArgument:&callback atIndex:2];
        callback(fileSize);
      }];
    });

    Given(@"^执行获取Appinfo成功返回的数据为$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      NSArray<NSArray<NSString *> *> *expect = userInfo[@"DataTable"];
      NSDictionary *dic = [self coverArrayToDicWith:expect];
      NSMutableDictionary *tempDic = [dic mutableCopy];
      if ([tempDic[@"envPreSet"] boolValue]) {
          tempDic[@"isPreViewEnv"] = @"true";
      }
      else {
          tempDic[@"isPreViewEnv"] = @"false";
      }
      tempDic[@"envPreSet"] = nil;
      dic = [tempDic copy];
      [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getAppInfo]) andReturn:dic];
    });

    When(@"^调用名称为\"(.*)\"的action,入参为\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      __weak typeof(self) weakSelf = self;

      NSDictionary *dic = @{};
      if ([args[1] isEqualToString:@"null"] || [args[1] isEqualToString:@"{}"]) {
          dic = [self coverInvalidJsonStrToDic:args[1]];
      }
      else {
          dic = jsonObjectFromEscapedString(args[1]);
      }

      [self.action execute:args[0]
                    params:dic
                   options:nil
               finishBlock:[[UPUnitTestCallBackIMP alloc] initWithCallback:^(id _Nonnull retData) {
                 weakSelf.executeResult = retData;
               }]];
    });

    Then(@"^执行\"(.*)\"失败,失败码:\"(.*)\",失败信息:\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert([self.executeResult[@"retCode"] isEqualToString:args[1]], @"执行%@失败,retCode:%@与预期不符", args[0], self.executeResult[@"retCode"]);
      CCIAssert([self.executeResult[@"retInfo"] isEqualToString:args[2]], @"执行%@失败,retInfo:%@与预期不符", args[0], self.executeResult[@"retInfo"]);
    });

    Then(@"^执行名称为\"(.*)\"的action成功,成功码:\"(.*)\",成功信息:\"(.*)\",返回数据:\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert([self.executeResult[@"retCode"] isEqualToString:args[1]], @"执行%@失败,retCode:%@与预期不符", args[0], self.executeResult[@"retCode"]);
      CCIAssert([self.executeResult[@"retInfo"] isEqualToString:args[2]], @"执行%@失败,retInfo:%@与预期不符", args[0], self.executeResult[@"retInfo"]);

      if ([self.executeResult[@"retData"] isKindOfClass:[NSDictionary class]]) {
          NSDictionary *expectRetData = jsonObjectFromEscapedString1(args[3]);
          CCIAssert(isDictionaryEqual(expectRetData, self.executeResult[@"retData"]), @"执行%@失败,retData%@与预期不符", args[3], expectRetData);
      }
      else {
          if ([args[0] isEqualToString:@"getCacheSizeForAppInfo"]) {
              long long excuteRet = [self.executeResult[@"retData"] longLongValue];
              long long argsRet = args[3].longLongValue;
              CCIAssert(excuteRet == argsRet, @"执行%@失败,retData%@与预期不符", args[0], @(argsRet));
          }
          else {
              BOOL excuteRet = [self.executeResult[@"retData"] boolValue];
              BOOL argsRet = args[3].boolValue;
              CCIAssert(excuteRet == argsRet, @"执行%@失败,retData%@与预期不符", args[0], @(argsRet));
          }
      }
    });

    Then(@"^执行名称为\"(.*)\"的action成功,成功码:\"(.*)\",成功信息:\"(.*)\",返回数据:$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
      CCIAssert([self.executeResult[@"retCode"] isEqualToString:args[1]], @"执行%@失败,retCode:%@与预期不符", args[0], self.executeResult[@"retCode"]);
      CCIAssert([self.executeResult[@"retInfo"] isEqualToString:args[2]], @"执行%@失败,retInfo:%@与预期不符", args[0], self.executeResult[@"retInfo"]);

      NSString *retDataStr = dictionaryToJson(self.executeResult[@"retData"]);
      retDataStr = sortedString(retDataStr);
      retDataStr = [self replaceQuotation:retDataStr];
      NSArray<NSArray<NSString *> *> *expect = userInfo[@"DataTable"];
      NSString *targetStr = dictionaryToJson([self coverArrayToDicWith:expect]);
      targetStr = sortedString(targetStr);
      targetStr = [self replaceQuotation:targetStr];
      CCIAssert([retDataStr isEqualToString:targetStr], @"执行%@失败,retData%@与预期不符", args[0], targetStr);
    });
}

- (NSDictionary *)coverArrayToDicWith:(NSArray *)array
{
    NSArray<NSString *> *keyArray = array.firstObject;
    NSArray *valueArray = array.lastObject;
    __block NSMutableDictionary *locationDic = NSMutableDictionary.dictionary;
    [keyArray enumerateObjectsUsingBlock:^(NSString *_Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      locationDic[keyArray[idx]] = valueArray[idx] ? valueArray[idx] : @"";
    }];
    return [locationDic copy];
}

- (NSDictionary *)coverInvalidJsonStrToDic:(NSString *)string
{
    if ([string isEqualToString:@"null"]) {
        return nil;
    }
    else {
        return @{};
    }
}

- (NSString *)replaceQuotation:(NSString *)targetString
{
    NSString *resultString = [targetString stringByReplacingOccurrencesOfString:@"\"" withString:@""];
    return resultString;
}

@end
