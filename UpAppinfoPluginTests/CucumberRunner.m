//
//  CucumberRunner.m
//  UpAppinfoPluginTests
//
//  Created by yaosixu on 2021/10/18.
//

#import "Cucumberish.h"
#import <XCTest/XCTest.h>
#import "AppinfoStep.h"


__attribute__((constructor)) void CucumberInit(void)
{
    [[Cucumberish instance] setPrettyNamesAllowed:NO];
    [Cucumberish instance].fixMissingLastScenario = YES;
    [AppinfoStep new];
    NSBundle *bundle = [NSBundle bundleForClass:[AppinfoStep class]];
    Cucumberish *cucumber = [[Cucumberish instance] parserFeaturesInDirectory:@"features" fromBundle:bundle includeTags:nil excludeTags:@[ @"ios_ignore", @"ignore" ]];
    [cucumber beginExecution];
}
