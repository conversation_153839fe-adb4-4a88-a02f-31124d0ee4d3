//
//  StepsUtils.h
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UpPluginFoundation/UpPluginFoundation.h>

NS_ASSUME_NONNULL_BEGIN


/// 将含有转义字符的 json 字符串转成 json 对象
/// @param escapedString escapedString
NSDictionary *jsonObjectFromEscapedString(NSString *escapedString);

/// 去掉转义字符并对其中的json字符串进行排序（只支持简单json，不支持多层级json）
/// @param escapedString escapedString
NSString *jsonStringFromEscapedStringNotSorted(NSString *escapedString);

/// 去掉转义字符
/// @param escapedString escapedString
NSString *jsonStringFromEscapedString(NSString *escapedString);

/// 字典转换为字符串
/// @param dic 字典
NSString *dictionaryToJson(NSDictionary *dic);

CallMethodPlatformType platformTypeWithString(NSString *str);

/// 字符串排序
/// @param str 字符串
NSString *sortedString(NSString *str);

/// 将含有转义字符的 json 字符串转成 json 对象
id jsonObjectFromEscapedString1(NSString *escapedString);

id _Nullable objectFromStepStringArg(NSString *stringArg);

BOOL isDictionaryEqual(NSDictionary *dict1, NSDictionary *dict2);

BOOL isArrayEqual(NSArray *array1, NSArray *array2);


NS_ASSUME_NONNULL_END
