//
//  StepsUtils.m
//  UPDeviceTests
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2020/12/24.
//  Copyright © 2020 海尔优家智能科技（北京）有限公司. All rights reserved.
//

#import "StepsUtils.h"


NSDictionary *jsonObjectFromEscapedString(NSString *escapedString)
{
    if (escapedString == nil) {
        return nil;
    }
    NSString *responseString = jsonStringFromEscapedStringNotSorted(escapedString);
    NSData *jsonData = [responseString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if (err) {
        NSLog(@"json解析失败：%@", err);
        return nil;
    }
    return dic;
}

NSMutableString *jsonStringFromEscapedStringNotSorted(NSString *escapedString)
{
    if (escapedString == nil) {
        return nil;
    }
    NSMutableString *responseString = [NSMutableString stringWithString:escapedString];
    NSString *character = nil;
    for (int i = 0; i < responseString.length; i++) {
        character = [responseString substringWithRange:NSMakeRange(i, 1)];
        if ([character isEqualToString:@"\\"])
            [responseString deleteCharactersInRange:NSMakeRange(i, 1)];
    }
    return responseString;
}

NSString *jsonStringFromEscapedString(NSString *escapedString)
{
    NSString *responseString = jsonStringFromEscapedStringNotSorted(escapedString);
    responseString = sortedString(responseString);
    return responseString;
}

NSString *escapedString(NSString *escapedString)
{
    if (escapedString == nil) {
        return nil;
    }
    NSMutableString *responseString = [NSMutableString stringWithString:escapedString];
    NSString *character = nil;
    for (int i = 0; i < responseString.length; i++) {
        character = [responseString substringWithRange:NSMakeRange(i, 1)];
        if ([character isEqualToString:@"\\"])
            [responseString deleteCharactersInRange:NSMakeRange(i, 1)];
    }
    return responseString;
}

NSString *dictionaryToJson(NSDictionary *dic)
{
    NSError *parseError = nil;
    NSData *jsonData = [NSJSONSerialization dataWithJSONObject:dic options:NSJSONWritingPrettyPrinted error:&parseError];
    NSString *res = [[NSString alloc] initWithData:jsonData encoding:NSUTF8StringEncoding];
    res = [res stringByReplacingOccurrencesOfString:@"\n" withString:@""];
    res = [res stringByReplacingOccurrencesOfString:@"\r" withString:@""];
    res = [res stringByReplacingOccurrencesOfString:@" " withString:@""];
    return res;
}

CallMethodPlatformType platformTypeWithString(NSString *str)
{
    CallMethodPlatformType platformType = 0;
    if ([str compare:@"flutter" options:NSCaseInsensitiveSearch] == NSOrderedSame) {
        platformType = CallMethodPlatformFlutter;
    }
    else {
        platformType = CallMethodPlatformH5;
    }
    return platformType;
}

NSString *sortedString(NSString *originStr)
{
    if ([originStr containsString:@"{"] && [originStr containsString:@"}"]) {
        NSArray *strArray = [originStr componentsSeparatedByString:@"}"];
        NSString *suffix = strArray[1];
        strArray = [strArray[0] componentsSeparatedByString:@"{"];
        NSString *preStr = strArray[0];
        NSString *str = strArray[1];
        str = [str stringByReplacingOccurrencesOfString:@"{" withString:@""];
        str = [str stringByReplacingOccurrencesOfString:@"}" withString:@""];
        NSArray *array = [str componentsSeparatedByString:@","];
        array = [array sortedArrayUsingComparator:^NSComparisonResult(NSString *_Nonnull obj1, NSString *_Nonnull obj2) {
          NSArray *tmp1 = [obj1 componentsSeparatedByString:@":"];
          NSArray *tmp2 = [obj2 componentsSeparatedByString:@":"];
          BOOL result = [tmp1[0] caseInsensitiveCompare:tmp2[0]] == NSOrderedDescending;
          return result;
        }];
        NSString *res = @"";
        for (NSString *str in array) {
            res = [NSString stringWithFormat:@"%@,%@", res, str];
        }
        if ([[res substringWithRange:NSMakeRange(0, 1)] isEqualToString:@","]) {
            res = [res stringByReplacingCharactersInRange:NSMakeRange(0, 1) withString:@""];
        }
        res = [NSString stringWithFormat:@"%@{%@", preStr, res];
        res = [NSString stringWithFormat:@"%@}%@", res, suffix];
        return res;
    }
    else {
        return originStr;
    }
}

id jsonObjectFromEscapedString1(NSString *escapedString)
{
    id nonJsonObj = objectFromStepStringArg(escapedString);
    if (!nonJsonObj) {
        return nil;
    }
    if (nonJsonObj && (![nonJsonObj isKindOfClass:NSString.class] || [nonJsonObj length] == 0)) {
        return nonJsonObj;
    }

    NSString *str = [escapedString stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    id result = str;
    if (([str hasPrefix:@"{"] && [str hasSuffix:@"}"]) ||
        ([str hasPrefix:@"["] && [str hasSuffix:@"]"])) {
        result = [NSJSONSerialization JSONObjectWithData:[[escapedString stringByReplacingOccurrencesOfString:@"\\" withString:@""] dataUsingEncoding:NSUTF8StringEncoding] options:NSJSONReadingMutableContainers | NSJSONReadingFragmentsAllowed error:NULL];
    }
    return result;
}

id _Nullable objectFromStepStringArg(NSString *stringArg)
{
    NSString *str = [stringArg stringByReplacingOccurrencesOfString:@"\\" withString:@""];
    id result = str;

    if ([str isEqualToString:@"null"]) {
        result = nil;
    }
    else if ([str isEqualToString:@"\"\""]) {
        result = @"";
    }
    else if (([@[ @"true", @"yes" ] containsObject:str.lowercaseString])) {
        result = @(YES);
    }
    else if ([@[ @"false", @"no" ] containsObject:str.lowercaseString]) {
        result = @(NO);
    }
    return result;
}

BOOL isDictionaryEqual(NSDictionary *dict1, NSDictionary *dict2)
{
    if (nil == dict1 && nil == dict2) {
        return YES;
    }

    if (nil == dict1 || nil == dict2) {
        return NO;
    }

    if (dict1.count != dict2.count) {
        return NO;
    }

    if (0 == dict1.count) {
        return YES;
    }

    __block BOOL isEqual = NO;
    [dict1 enumerateKeysAndObjectsUsingBlock:^(id _Nonnull key, id _Nonnull obj, BOOL *_Nonnull stop) {
      id obj2 = dict2[key];
      if (nil == obj2) {
          isEqual = NO;
          *stop = YES;
          return;
      }

      if ([obj isKindOfClass:NSDictionary.class]) {
          isEqual = isDictionaryEqual(obj, obj2);
      }
      else if ([obj isKindOfClass:[NSArray class]]) {
          isEqual = isArrayEqual(obj, obj2);
      }
      else {
          isEqual = [obj isEqual:obj2];
      }
      if (NO == isEqual) {
          *stop = YES;
          return;
      }
    }];

    return isEqual;
}

BOOL isArrayEqual(NSArray *array1, NSArray *array2)
{
    if (nil == array1 && nil == array2) {
        return YES;
    }
    if (nil == array1 || nil == array2) {
        return NO;
    }
    if (array1.count != array2.count) {
        return NO;
    }
    __block BOOL isEqual = NO;
    [array1 enumerateObjectsUsingBlock:^(id _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      id obj2 = array2[idx];
      if ([obj isKindOfClass:NSDictionary.class]) {
          isEqual = isDictionaryEqual(obj, obj2);
      }
      else if ([obj isKindOfClass:NSArray.class]) {
          isEqual = isArrayEqual(obj, obj2);
      }
      else {
          isEqual = [obj isEqual:obj2];
      }
      if (NO == isEqual) {
          *stop = YES;
          return;
      }
    }];
    return isEqual;
}
