// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 51;
	objects = {

/* Begin PBXBuildFile section */
		140BDD152733C60300500DFD /* DeviceModels.plist in Resources */ = {isa = PBXBuildFile; fileRef = 140BDD142733C60300500DFD /* DeviceModels.plist */; };
		145B75542717C86C00E3DDCF /* UpGetAppInfoAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 145B75522717C86C00E3DDCF /* UpGetAppInfoAction.h */; };
		145B75552717C86C00E3DDCF /* UpGetAppInfoAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 145B75532717C86C00E3DDCF /* UpGetAppInfoAction.m */; };
		145B755C27180A7C00E3DDCF /* UpIsGrayModeAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 145B755A27180A7C00E3DDCF /* UpIsGrayModeAction.h */; };
		145B755D27180A7C00E3DDCF /* UpIsGrayModeAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 145B755B27180A7C00E3DDCF /* UpIsGrayModeAction.m */; };
		145B756027180E7B00E3DDCF /* UpUpdateGrayModeAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 145B755E27180E7B00E3DDCF /* UpUpdateGrayModeAction.h */; };
		145B756127180E7B00E3DDCF /* UpUpdateGrayModeAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 145B755F27180E7B00E3DDCF /* UpUpdateGrayModeAction.m */; };
		145B758C271CFEAF00E3DDCF /* UpAppinfoPlugin.podspec in Resources */ = {isa = PBXBuildFile; fileRef = 145B758B271CFEAF00E3DDCF /* UpAppinfoPlugin.podspec */; };
		145B7595271D3F5800E3DDCF /* UpAppinfoPlugin.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1476EE5E271523EE00D3EC80 /* UpAppinfoPlugin.framework */; };
		145B75A0271D41DE00E3DDCF /* CucumberRunner.m in Sources */ = {isa = PBXBuildFile; fileRef = 145B759F271D41DE00E3DDCF /* CucumberRunner.m */; };
		145B75A3271D427700E3DDCF /* AppinfoStep.m in Sources */ = {isa = PBXBuildFile; fileRef = 145B75A2271D427700E3DDCF /* AppinfoStep.m */; };
		145B75A6271D42F200E3DDCF /* UPUnitTestCallBackIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = 145B75A5271D42F200E3DDCF /* UPUnitTestCallBackIMP.m */; };
		145B75A9271D460700E3DDCF /* StepsUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 145B75A8271D460700E3DDCF /* StepsUtils.m */; };
		145B75AB271E9A6F00E3DDCF /* features in Resources */ = {isa = PBXBuildFile; fileRef = 145B75AA271E9A6F00E3DDCF /* features */; };
		14640470274B704D00E1A37B /* UpGetCacheFilesSizeAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 1464046E274B704D00E1A37B /* UpGetCacheFilesSizeAction.h */; };
		14640471274B704D00E1A37B /* UpGetCacheFilesSizeAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 1464046F274B704D00E1A37B /* UpGetCacheFilesSizeAction.m */; };
		14640474274B706200E1A37B /* UpCleanCacheFilesAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 14640472274B706100E1A37B /* UpCleanCacheFilesAction.h */; };
		14640475274B706200E1A37B /* UpCleanCacheFilesAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 14640473274B706100E1A37B /* UpCleanCacheFilesAction.m */; };
		1465B1DE27265551006D9FF0 /* AppDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 1465B1DD27265551006D9FF0 /* AppDelegate.m */; };
		1465B1E127265551006D9FF0 /* SceneDelegate.m in Sources */ = {isa = PBXBuildFile; fileRef = 1465B1E027265551006D9FF0 /* SceneDelegate.m */; };
		1465B1E427265551006D9FF0 /* ViewController.m in Sources */ = {isa = PBXBuildFile; fileRef = 1465B1E327265551006D9FF0 /* ViewController.m */; };
		1465B1E727265551006D9FF0 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1465B1E527265551006D9FF0 /* Main.storyboard */; };
		1465B1E927265553006D9FF0 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1465B1E827265553006D9FF0 /* Assets.xcassets */; };
		1465B1EC27265553006D9FF0 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 1465B1EA27265553006D9FF0 /* LaunchScreen.storyboard */; };
		1465B1EF27265553006D9FF0 /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 1465B1EE27265553006D9FF0 /* main.m */; };
		14742ECB2716667F0006B631 /* UpAppinfoPluginManager.h in Headers */ = {isa = PBXBuildFile; fileRef = 14742EC92716667F0006B631 /* UpAppinfoPluginManager.h */; settings = {ATTRIBUTES = (Public, ); }; };
		14742ECC2716667F0006B631 /* UpAppinfoPluginManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 14742ECA2716667F0006B631 /* UpAppinfoPluginManager.m */; };
		1476EE62271523EE00D3EC80 /* UpAppinfoPlugin.h in Headers */ = {isa = PBXBuildFile; fileRef = 1476EE61271523EE00D3EC80 /* UpAppinfoPlugin.h */; settings = {ATTRIBUTES = (Public, ); }; };
		1476EE842715283100D3EC80 /* UpGetPhoneInfoAction.h in Headers */ = {isa = PBXBuildFile; fileRef = 1476EE822715283100D3EC80 /* UpGetPhoneInfoAction.h */; };
		1476EE852715283100D3EC80 /* UpGetPhoneInfoAction.m in Sources */ = {isa = PBXBuildFile; fileRef = 1476EE832715283100D3EC80 /* UpGetPhoneInfoAction.m */; };
		14B9B21827315BB7004290AA /* UpAppinfoPluginFramework.h in Headers */ = {isa = PBXBuildFile; fileRef = 14B9B21727315BB7004290AA /* UpAppinfoPluginFramework.h */; settings = {ATTRIBUTES = (Public, ); }; };
		14B9B22B27315DD1004290AA /* UpAppinfoPluginIMP.h in Headers */ = {isa = PBXBuildFile; fileRef = 14B9B22627315DD1004290AA /* UpAppinfoPluginIMP.h */; settings = {ATTRIBUTES = (Public, ); }; };
		14B9B22C27315DD1004290AA /* UpAppinfoUtils.m in Sources */ = {isa = PBXBuildFile; fileRef = 14B9B22727315DD1004290AA /* UpAppinfoUtils.m */; };
		14B9B22D27315DD1004290AA /* UpAppinfoPluginIMP.m in Sources */ = {isa = PBXBuildFile; fileRef = 14B9B22827315DD1004290AA /* UpAppinfoPluginIMP.m */; };
		14B9B22E27315DD1004290AA /* UpAppInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 14B9B22927315DD1004290AA /* UpAppInfoDelegate.h */; settings = {ATTRIBUTES = (Public, ); }; };
		14B9B22F27315DD1004290AA /* UpAppinfoUtils.h in Headers */ = {isa = PBXBuildFile; fileRef = 14B9B22A27315DD1004290AA /* UpAppinfoUtils.h */; };
		39AFA2F03F33D63EB2F43C91 /* libPods-UpAppinfoPlugin.a in Frameworks */ = {isa = PBXBuildFile; fileRef = C2327B1B3503F4BCDF80579D /* libPods-UpAppinfoPlugin.a */; };
		3D60B6B0758027A9BF236983 /* libPods-UpAppinfoPluginTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = A1D1837B67F96F469C1BED64 /* libPods-UpAppinfoPluginTests.a */; };
		5EB130D92B3AAAA90004103D /* UpFunctionToggleAction.swift in Sources */ = {isa = PBXBuildFile; fileRef = 5EB130D82B3AAAA90004103D /* UpFunctionToggleAction.swift */; };
		5EB130DA2B3AB8F90004103D /* UpAppInfoDelegate.h in Headers */ = {isa = PBXBuildFile; fileRef = 14B9B22927315DD1004290AA /* UpAppInfoDelegate.h */; };
		5EB130DB2B3AC5000004103D /* UpAppinfoPluginIMP.h in Headers */ = {isa = PBXBuildFile; fileRef = 14B9B22627315DD1004290AA /* UpAppinfoPluginIMP.h */; settings = {ATTRIBUTES = (Public, ); }; };
		D10B2F1ED18C3E8C57B37A47 /* libPods-UpAppinfoPluginDebuger.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 9366FD66DD985CAAF68DED24 /* libPods-UpAppinfoPluginDebuger.a */; };
		F24D470A155F2EEC8CBD6C9D /* libPods-UpAppinfoPluginFramework.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5AADC6E30A4F0927183A550B /* libPods-UpAppinfoPluginFramework.a */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		145B7596271D3F5800E3DDCF /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1476EE55271523EE00D3EC80 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 1476EE5D271523EE00D3EC80;
			remoteInfo = UpAppinfoPlugin;
		};
		14B9B23027315FD0004290AA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1476EE55271523EE00D3EC80 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 14B9B21427315BB7004290AA;
			remoteInfo = UpAppinfoPluginFramework;
		};
		14B9B2322731617E004290AA /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 1476EE55271523EE00D3EC80 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 14B9B21427315BB7004290AA;
			remoteInfo = UpAppinfoPluginFramework;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		0BF0A55F3E7FEF0DF828C4D4 /* Pods-UpAppinfoPlugin.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpAppinfoPlugin.debug.xcconfig"; path = "Target Support Files/Pods-UpAppinfoPlugin/Pods-UpAppinfoPlugin.debug.xcconfig"; sourceTree = "<group>"; };
		140BDD142733C60300500DFD /* DeviceModels.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = DeviceModels.plist; sourceTree = "<group>"; };
		145B75522717C86C00E3DDCF /* UpGetAppInfoAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpGetAppInfoAction.h; sourceTree = "<group>"; };
		145B75532717C86C00E3DDCF /* UpGetAppInfoAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpGetAppInfoAction.m; sourceTree = "<group>"; };
		145B755A27180A7C00E3DDCF /* UpIsGrayModeAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpIsGrayModeAction.h; sourceTree = "<group>"; };
		145B755B27180A7C00E3DDCF /* UpIsGrayModeAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpIsGrayModeAction.m; sourceTree = "<group>"; };
		145B755E27180E7B00E3DDCF /* UpUpdateGrayModeAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpUpdateGrayModeAction.h; sourceTree = "<group>"; };
		145B755F27180E7B00E3DDCF /* UpUpdateGrayModeAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpUpdateGrayModeAction.m; sourceTree = "<group>"; };
		145B7587271CFDA000E3DDCF /* release.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = release.md; sourceTree = "<group>"; };
		145B7588271CFDA000E3DDCF /* readme.md */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = net.daringfireball.markdown; path = readme.md; sourceTree = "<group>"; };
		145B758B271CFEAF00E3DDCF /* UpAppinfoPlugin.podspec */ = {isa = PBXFileReference; lastKnownFileType = text; path = UpAppinfoPlugin.podspec; sourceTree = SOURCE_ROOT; xcLanguageSpecificationIdentifier = xcode.lang.ruby; };
		145B7591271D3F5800E3DDCF /* UpAppinfoPluginTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = UpAppinfoPluginTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		145B759F271D41DE00E3DDCF /* CucumberRunner.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CucumberRunner.m; sourceTree = "<group>"; };
		145B75A1271D427700E3DDCF /* AppinfoStep.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppinfoStep.h; sourceTree = "<group>"; };
		145B75A2271D427700E3DDCF /* AppinfoStep.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppinfoStep.m; sourceTree = "<group>"; };
		145B75A4271D42F200E3DDCF /* UPUnitTestCallBackIMP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UPUnitTestCallBackIMP.h; sourceTree = "<group>"; };
		145B75A5271D42F200E3DDCF /* UPUnitTestCallBackIMP.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UPUnitTestCallBackIMP.m; sourceTree = "<group>"; };
		145B75A7271D460700E3DDCF /* StepsUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = StepsUtils.h; sourceTree = "<group>"; };
		145B75A8271D460700E3DDCF /* StepsUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = StepsUtils.m; sourceTree = "<group>"; };
		145B75AA271E9A6F00E3DDCF /* features */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = features; sourceTree = SOURCE_ROOT; };
		1464046E274B704D00E1A37B /* UpGetCacheFilesSizeAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpGetCacheFilesSizeAction.h; sourceTree = "<group>"; };
		1464046F274B704D00E1A37B /* UpGetCacheFilesSizeAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpGetCacheFilesSizeAction.m; sourceTree = "<group>"; };
		14640472274B706100E1A37B /* UpCleanCacheFilesAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpCleanCacheFilesAction.h; sourceTree = "<group>"; };
		14640473274B706100E1A37B /* UpCleanCacheFilesAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpCleanCacheFilesAction.m; sourceTree = "<group>"; };
		1465B1DA27265551006D9FF0 /* UpAppinfoPluginDebuger.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = UpAppinfoPluginDebuger.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1465B1DC27265551006D9FF0 /* AppDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = AppDelegate.h; sourceTree = "<group>"; };
		1465B1DD27265551006D9FF0 /* AppDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = AppDelegate.m; sourceTree = "<group>"; };
		1465B1DF27265551006D9FF0 /* SceneDelegate.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = SceneDelegate.h; sourceTree = "<group>"; };
		1465B1E027265551006D9FF0 /* SceneDelegate.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SceneDelegate.m; sourceTree = "<group>"; };
		1465B1E227265551006D9FF0 /* ViewController.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = ViewController.h; sourceTree = "<group>"; };
		1465B1E327265551006D9FF0 /* ViewController.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ViewController.m; sourceTree = "<group>"; };
		1465B1E627265551006D9FF0 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		1465B1E827265553006D9FF0 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1465B1EB27265553006D9FF0 /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		1465B1ED27265553006D9FF0 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		1465B1EE27265553006D9FF0 /* main.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = main.m; sourceTree = "<group>"; };
		14742EC92716667F0006B631 /* UpAppinfoPluginManager.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAppinfoPluginManager.h; sourceTree = "<group>"; };
		14742ECA2716667F0006B631 /* UpAppinfoPluginManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpAppinfoPluginManager.m; sourceTree = "<group>"; };
		1476EE5E271523EE00D3EC80 /* UpAppinfoPlugin.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = UpAppinfoPlugin.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		1476EE61271523EE00D3EC80 /* UpAppinfoPlugin.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAppinfoPlugin.h; sourceTree = "<group>"; };
		1476EE822715283100D3EC80 /* UpGetPhoneInfoAction.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpGetPhoneInfoAction.h; sourceTree = "<group>"; };
		1476EE832715283100D3EC80 /* UpGetPhoneInfoAction.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = UpGetPhoneInfoAction.m; sourceTree = "<group>"; };
		1476EE862715297C00D3EC80 /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		14B9B21527315BB7004290AA /* UpAppinfoPluginFramework.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = UpAppinfoPluginFramework.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		14B9B21727315BB7004290AA /* UpAppinfoPluginFramework.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = UpAppinfoPluginFramework.h; sourceTree = "<group>"; };
		14B9B22627315DD1004290AA /* UpAppinfoPluginIMP.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpAppinfoPluginIMP.h; sourceTree = "<group>"; };
		14B9B22727315DD1004290AA /* UpAppinfoUtils.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpAppinfoUtils.m; sourceTree = "<group>"; };
		14B9B22827315DD1004290AA /* UpAppinfoPluginIMP.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = UpAppinfoPluginIMP.m; sourceTree = "<group>"; };
		14B9B22927315DD1004290AA /* UpAppInfoDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpAppInfoDelegate.h; sourceTree = "<group>"; };
		14B9B22A27315DD1004290AA /* UpAppinfoUtils.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = UpAppinfoUtils.h; sourceTree = "<group>"; };
		1D148A4563E3A55965E51E0C /* Pods-UpAppinfoPluginFramework.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpAppinfoPluginFramework.debug.xcconfig"; path = "Target Support Files/Pods-UpAppinfoPluginFramework/Pods-UpAppinfoPluginFramework.debug.xcconfig"; sourceTree = "<group>"; };
		4EF7B7E10AA43FBA6BC9ABEA /* Pods-UpAppinfoPlugin.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpAppinfoPlugin.release.xcconfig"; path = "Target Support Files/Pods-UpAppinfoPlugin/Pods-UpAppinfoPlugin.release.xcconfig"; sourceTree = "<group>"; };
		508ADEA2E1219BEDB58DE614 /* Pods-UpAppinfoPluginDebuger.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpAppinfoPluginDebuger.debug.xcconfig"; path = "Target Support Files/Pods-UpAppinfoPluginDebuger/Pods-UpAppinfoPluginDebuger.debug.xcconfig"; sourceTree = "<group>"; };
		5AADC6E30A4F0927183A550B /* libPods-UpAppinfoPluginFramework.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpAppinfoPluginFramework.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		5EB130D82B3AAAA90004103D /* UpFunctionToggleAction.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpFunctionToggleAction.swift; sourceTree = "<group>"; };
		61006F51E72248EF8225B598 /* Pods-UpAppinfoPluginFramework.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpAppinfoPluginFramework.release.xcconfig"; path = "Target Support Files/Pods-UpAppinfoPluginFramework/Pods-UpAppinfoPluginFramework.release.xcconfig"; sourceTree = "<group>"; };
		7BFD3AC02D49EDA534B42BF1 /* Pods-UpAppinfoPluginDebuger.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpAppinfoPluginDebuger.release.xcconfig"; path = "Target Support Files/Pods-UpAppinfoPluginDebuger/Pods-UpAppinfoPluginDebuger.release.xcconfig"; sourceTree = "<group>"; };
		9366FD66DD985CAAF68DED24 /* libPods-UpAppinfoPluginDebuger.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpAppinfoPluginDebuger.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		A1D1837B67F96F469C1BED64 /* libPods-UpAppinfoPluginTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpAppinfoPluginTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		B12B925F6282478A38BC0DE7 /* Pods-UpAppinfoPluginTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpAppinfoPluginTests.debug.xcconfig"; path = "Target Support Files/Pods-UpAppinfoPluginTests/Pods-UpAppinfoPluginTests.debug.xcconfig"; sourceTree = "<group>"; };
		C2327B1B3503F4BCDF80579D /* libPods-UpAppinfoPlugin.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-UpAppinfoPlugin.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		D51D0B01051BC4D7538227BB /* Pods-UpAppinfoPluginTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-UpAppinfoPluginTests.release.xcconfig"; path = "Target Support Files/Pods-UpAppinfoPluginTests/Pods-UpAppinfoPluginTests.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		145B758E271D3F5800E3DDCF /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				145B7595271D3F5800E3DDCF /* UpAppinfoPlugin.framework in Frameworks */,
				3D60B6B0758027A9BF236983 /* libPods-UpAppinfoPluginTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1465B1D727265551006D9FF0 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				D10B2F1ED18C3E8C57B37A47 /* libPods-UpAppinfoPluginDebuger.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1476EE5B271523EE00D3EC80 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				39AFA2F03F33D63EB2F43C91 /* libPods-UpAppinfoPlugin.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14B9B21227315BB7004290AA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				F24D470A155F2EEC8CBD6C9D /* libPods-UpAppinfoPluginFramework.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		145B7592271D3F5800E3DDCF /* UpAppinfoPluginTests */ = {
			isa = PBXGroup;
			children = (
				145B75AA271E9A6F00E3DDCF /* features */,
				145B759C271D3FEB00E3DDCF /* Utils */,
				145B759B271D3FD900E3DDCF /* Steps */,
				145B759F271D41DE00E3DDCF /* CucumberRunner.m */,
			);
			path = UpAppinfoPluginTests;
			sourceTree = "<group>";
		};
		145B759B271D3FD900E3DDCF /* Steps */ = {
			isa = PBXGroup;
			children = (
				145B75A1271D427700E3DDCF /* AppinfoStep.h */,
				145B75A2271D427700E3DDCF /* AppinfoStep.m */,
			);
			path = Steps;
			sourceTree = "<group>";
		};
		145B759C271D3FEB00E3DDCF /* Utils */ = {
			isa = PBXGroup;
			children = (
				145B75A7271D460700E3DDCF /* StepsUtils.h */,
				145B75A8271D460700E3DDCF /* StepsUtils.m */,
				145B75A4271D42F200E3DDCF /* UPUnitTestCallBackIMP.h */,
				145B75A5271D42F200E3DDCF /* UPUnitTestCallBackIMP.m */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		1465B1DB27265551006D9FF0 /* UpAppinfoPluginDebuger */ = {
			isa = PBXGroup;
			children = (
				1465B1DC27265551006D9FF0 /* AppDelegate.h */,
				1465B1DD27265551006D9FF0 /* AppDelegate.m */,
				1465B1DF27265551006D9FF0 /* SceneDelegate.h */,
				1465B1E027265551006D9FF0 /* SceneDelegate.m */,
				1465B1E227265551006D9FF0 /* ViewController.h */,
				1465B1E327265551006D9FF0 /* ViewController.m */,
				1465B1E527265551006D9FF0 /* Main.storyboard */,
				1465B1E827265553006D9FF0 /* Assets.xcassets */,
				1465B1EA27265553006D9FF0 /* LaunchScreen.storyboard */,
				1465B1ED27265553006D9FF0 /* Info.plist */,
				1465B1EE27265553006D9FF0 /* main.m */,
			);
			path = UpAppinfoPluginDebuger;
			sourceTree = "<group>";
		};
		1476EE54271523EE00D3EC80 = {
			isa = PBXGroup;
			children = (
				1476EE60271523EE00D3EC80 /* UpAppinfoPlugin */,
				145B7592271D3F5800E3DDCF /* UpAppinfoPluginTests */,
				1465B1DB27265551006D9FF0 /* UpAppinfoPluginDebuger */,
				14B9B21627315BB7004290AA /* UpAppinfoPluginFramework */,
				1476EE5F271523EE00D3EC80 /* Products */,
				C068B4212B2B67619F9B3B35 /* Pods */,
				22357272EE9B66DF8E56B8B8 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		1476EE5F271523EE00D3EC80 /* Products */ = {
			isa = PBXGroup;
			children = (
				1476EE5E271523EE00D3EC80 /* UpAppinfoPlugin.framework */,
				145B7591271D3F5800E3DDCF /* UpAppinfoPluginTests.xctest */,
				1465B1DA27265551006D9FF0 /* UpAppinfoPluginDebuger.app */,
				14B9B21527315BB7004290AA /* UpAppinfoPluginFramework.framework */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1476EE60271523EE00D3EC80 /* UpAppinfoPlugin */ = {
			isa = PBXGroup;
			children = (
				1476EE7F2715264600D3EC80 /* Plugin */,
				1476EE802715265200D3EC80 /* Action */,
				1476EE812715265800D3EC80 /* doc */,
				1476EE61271523EE00D3EC80 /* UpAppinfoPlugin.h */,
				1476EE862715297C00D3EC80 /* Info.plist */,
			);
			path = UpAppinfoPlugin;
			sourceTree = "<group>";
		};
		1476EE7F2715264600D3EC80 /* Plugin */ = {
			isa = PBXGroup;
			children = (
				14742EC92716667F0006B631 /* UpAppinfoPluginManager.h */,
				14742ECA2716667F0006B631 /* UpAppinfoPluginManager.m */,
				140BDD142733C60300500DFD /* DeviceModels.plist */,
			);
			path = Plugin;
			sourceTree = "<group>";
		};
		1476EE802715265200D3EC80 /* Action */ = {
			isa = PBXGroup;
			children = (
				1476EE822715283100D3EC80 /* UpGetPhoneInfoAction.h */,
				1476EE832715283100D3EC80 /* UpGetPhoneInfoAction.m */,
				145B75522717C86C00E3DDCF /* UpGetAppInfoAction.h */,
				145B75532717C86C00E3DDCF /* UpGetAppInfoAction.m */,
				145B755A27180A7C00E3DDCF /* UpIsGrayModeAction.h */,
				145B755B27180A7C00E3DDCF /* UpIsGrayModeAction.m */,
				145B755E27180E7B00E3DDCF /* UpUpdateGrayModeAction.h */,
				145B755F27180E7B00E3DDCF /* UpUpdateGrayModeAction.m */,
				1464046E274B704D00E1A37B /* UpGetCacheFilesSizeAction.h */,
				1464046F274B704D00E1A37B /* UpGetCacheFilesSizeAction.m */,
				14640472274B706100E1A37B /* UpCleanCacheFilesAction.h */,
				14640473274B706100E1A37B /* UpCleanCacheFilesAction.m */,
				5EB130D82B3AAAA90004103D /* UpFunctionToggleAction.swift */,
			);
			path = Action;
			sourceTree = "<group>";
		};
		1476EE812715265800D3EC80 /* doc */ = {
			isa = PBXGroup;
			children = (
				145B758B271CFEAF00E3DDCF /* UpAppinfoPlugin.podspec */,
				145B7588271CFDA000E3DDCF /* readme.md */,
				145B7587271CFDA000E3DDCF /* release.md */,
			);
			path = doc;
			sourceTree = "<group>";
		};
		14B9B21627315BB7004290AA /* UpAppinfoPluginFramework */ = {
			isa = PBXGroup;
			children = (
				14B9B22927315DD1004290AA /* UpAppInfoDelegate.h */,
				14B9B22627315DD1004290AA /* UpAppinfoPluginIMP.h */,
				14B9B22827315DD1004290AA /* UpAppinfoPluginIMP.m */,
				14B9B22A27315DD1004290AA /* UpAppinfoUtils.h */,
				14B9B22727315DD1004290AA /* UpAppinfoUtils.m */,
				14B9B21727315BB7004290AA /* UpAppinfoPluginFramework.h */,
			);
			path = UpAppinfoPluginFramework;
			sourceTree = "<group>";
		};
		22357272EE9B66DF8E56B8B8 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				C2327B1B3503F4BCDF80579D /* libPods-UpAppinfoPlugin.a */,
				A1D1837B67F96F469C1BED64 /* libPods-UpAppinfoPluginTests.a */,
				9366FD66DD985CAAF68DED24 /* libPods-UpAppinfoPluginDebuger.a */,
				5AADC6E30A4F0927183A550B /* libPods-UpAppinfoPluginFramework.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		C068B4212B2B67619F9B3B35 /* Pods */ = {
			isa = PBXGroup;
			children = (
				0BF0A55F3E7FEF0DF828C4D4 /* Pods-UpAppinfoPlugin.debug.xcconfig */,
				4EF7B7E10AA43FBA6BC9ABEA /* Pods-UpAppinfoPlugin.release.xcconfig */,
				B12B925F6282478A38BC0DE7 /* Pods-UpAppinfoPluginTests.debug.xcconfig */,
				D51D0B01051BC4D7538227BB /* Pods-UpAppinfoPluginTests.release.xcconfig */,
				508ADEA2E1219BEDB58DE614 /* Pods-UpAppinfoPluginDebuger.debug.xcconfig */,
				7BFD3AC02D49EDA534B42BF1 /* Pods-UpAppinfoPluginDebuger.release.xcconfig */,
				1D148A4563E3A55965E51E0C /* Pods-UpAppinfoPluginFramework.debug.xcconfig */,
				61006F51E72248EF8225B598 /* Pods-UpAppinfoPluginFramework.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		1476EE59271523EE00D3EC80 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1476EE62271523EE00D3EC80 /* UpAppinfoPlugin.h in Headers */,
				5EB130DB2B3AC5000004103D /* UpAppinfoPluginIMP.h in Headers */,
				14742ECB2716667F0006B631 /* UpAppinfoPluginManager.h in Headers */,
				5EB130DA2B3AB8F90004103D /* UpAppInfoDelegate.h in Headers */,
				145B75542717C86C00E3DDCF /* UpGetAppInfoAction.h in Headers */,
				145B755C27180A7C00E3DDCF /* UpIsGrayModeAction.h in Headers */,
				14640470274B704D00E1A37B /* UpGetCacheFilesSizeAction.h in Headers */,
				1476EE842715283100D3EC80 /* UpGetPhoneInfoAction.h in Headers */,
				14640474274B706200E1A37B /* UpCleanCacheFilesAction.h in Headers */,
				145B756027180E7B00E3DDCF /* UpUpdateGrayModeAction.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14B9B21027315BB7004290AA /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				14B9B21827315BB7004290AA /* UpAppinfoPluginFramework.h in Headers */,
				14B9B22B27315DD1004290AA /* UpAppinfoPluginIMP.h in Headers */,
				14B9B22E27315DD1004290AA /* UpAppInfoDelegate.h in Headers */,
				14B9B22F27315DD1004290AA /* UpAppinfoUtils.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		145B7590271D3F5800E3DDCF /* UpAppinfoPluginTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 145B7598271D3F5800E3DDCF /* Build configuration list for PBXNativeTarget "UpAppinfoPluginTests" */;
			buildPhases = (
				DF76FA68C6BCAAF7C017D826 /* [CP] Check Pods Manifest.lock */,
				145B758D271D3F5800E3DDCF /* Sources */,
				145B758E271D3F5800E3DDCF /* Frameworks */,
				145B758F271D3F5800E3DDCF /* Resources */,
				807D271D8EB23CB7AF741A75 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				14B9B2332731617E004290AA /* PBXTargetDependency */,
				145B7597271D3F5800E3DDCF /* PBXTargetDependency */,
			);
			name = UpAppinfoPluginTests;
			productName = UpAppinfoPluginTests;
			productReference = 145B7591271D3F5800E3DDCF /* UpAppinfoPluginTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		1465B1D927265551006D9FF0 /* UpAppinfoPluginDebuger */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1465B1F227265553006D9FF0 /* Build configuration list for PBXNativeTarget "UpAppinfoPluginDebuger" */;
			buildPhases = (
				9CA9522477799AB4AB80AA04 /* [CP] Check Pods Manifest.lock */,
				1465B1D627265551006D9FF0 /* Sources */,
				1465B1D727265551006D9FF0 /* Frameworks */,
				1465B1D827265551006D9FF0 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UpAppinfoPluginDebuger;
			productName = UpAppinfoPluginDebuger;
			productReference = 1465B1DA27265551006D9FF0 /* UpAppinfoPluginDebuger.app */;
			productType = "com.apple.product-type.application";
		};
		1476EE5D271523EE00D3EC80 /* UpAppinfoPlugin */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1476EE65271523EE00D3EC80 /* Build configuration list for PBXNativeTarget "UpAppinfoPlugin" */;
			buildPhases = (
				BCF081582F6BBD5B70433AD2 /* [CP] Check Pods Manifest.lock */,
				1476EE59271523EE00D3EC80 /* Headers */,
				1476EE5A271523EE00D3EC80 /* Sources */,
				1476EE5B271523EE00D3EC80 /* Frameworks */,
				1476EE5C271523EE00D3EC80 /* Resources */,
				399BC914FDD813FC6FB35C4F /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				14B9B23127315FD0004290AA /* PBXTargetDependency */,
			);
			name = UpAppinfoPlugin;
			productName = UpAppinfoPlugin;
			productReference = 1476EE5E271523EE00D3EC80 /* UpAppinfoPlugin.framework */;
			productType = "com.apple.product-type.framework";
		};
		14B9B21427315BB7004290AA /* UpAppinfoPluginFramework */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 14B9B21B27315BB7004290AA /* Build configuration list for PBXNativeTarget "UpAppinfoPluginFramework" */;
			buildPhases = (
				B3DB58260C8D22BEECC6B626 /* [CP] Check Pods Manifest.lock */,
				14B9B21027315BB7004290AA /* Headers */,
				14B9B21127315BB7004290AA /* Sources */,
				14B9B21227315BB7004290AA /* Frameworks */,
				14B9B21327315BB7004290AA /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = UpAppinfoPluginFramework;
			productName = UpAppinfoPluginFramework;
			productReference = 14B9B21527315BB7004290AA /* UpAppinfoPluginFramework.framework */;
			productType = "com.apple.product-type.framework";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1476EE55271523EE00D3EC80 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1300;
				TargetAttributes = {
					145B7590271D3F5800E3DDCF = {
						CreatedOnToolsVersion = 13.0;
					};
					1465B1D927265551006D9FF0 = {
						CreatedOnToolsVersion = 13.0;
					};
					1476EE5D271523EE00D3EC80 = {
						CreatedOnToolsVersion = 13.0;
						LastSwiftMigration = 1430;
					};
					14B9B21427315BB7004290AA = {
						CreatedOnToolsVersion = 13.0;
					};
				};
			};
			buildConfigurationList = 1476EE58271523EE00D3EC80 /* Build configuration list for PBXProject "UpAppinfoPlugin" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1476EE54271523EE00D3EC80;
			productRefGroup = 1476EE5F271523EE00D3EC80 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1476EE5D271523EE00D3EC80 /* UpAppinfoPlugin */,
				145B7590271D3F5800E3DDCF /* UpAppinfoPluginTests */,
				1465B1D927265551006D9FF0 /* UpAppinfoPluginDebuger */,
				14B9B21427315BB7004290AA /* UpAppinfoPluginFramework */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		145B758F271D3F5800E3DDCF /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				145B75AB271E9A6F00E3DDCF /* features in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1465B1D827265551006D9FF0 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1465B1EC27265553006D9FF0 /* LaunchScreen.storyboard in Resources */,
				1465B1E927265553006D9FF0 /* Assets.xcassets in Resources */,
				1465B1E727265551006D9FF0 /* Main.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1476EE5C271523EE00D3EC80 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				140BDD152733C60300500DFD /* DeviceModels.plist in Resources */,
				145B758C271CFEAF00E3DDCF /* UpAppinfoPlugin.podspec in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14B9B21327315BB7004290AA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		399BC914FDD813FC6FB35C4F /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpAppinfoPlugin/Pods-UpAppinfoPlugin-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpAppinfoPlugin/Pods-UpAppinfoPlugin-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UpAppinfoPlugin/Pods-UpAppinfoPlugin-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		807D271D8EB23CB7AF741A75 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpAppinfoPluginTests/Pods-UpAppinfoPluginTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-UpAppinfoPluginTests/Pods-UpAppinfoPluginTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-UpAppinfoPluginTests/Pods-UpAppinfoPluginTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		9CA9522477799AB4AB80AA04 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpAppinfoPluginDebuger-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		B3DB58260C8D22BEECC6B626 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpAppinfoPluginFramework-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		BCF081582F6BBD5B70433AD2 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpAppinfoPlugin-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DF76FA68C6BCAAF7C017D826 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-UpAppinfoPluginTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		145B758D271D3F5800E3DDCF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				145B75A6271D42F200E3DDCF /* UPUnitTestCallBackIMP.m in Sources */,
				145B75A0271D41DE00E3DDCF /* CucumberRunner.m in Sources */,
				145B75A3271D427700E3DDCF /* AppinfoStep.m in Sources */,
				145B75A9271D460700E3DDCF /* StepsUtils.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1465B1D627265551006D9FF0 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1465B1E427265551006D9FF0 /* ViewController.m in Sources */,
				1465B1DE27265551006D9FF0 /* AppDelegate.m in Sources */,
				1465B1EF27265553006D9FF0 /* main.m in Sources */,
				1465B1E127265551006D9FF0 /* SceneDelegate.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		1476EE5A271523EE00D3EC80 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				145B755D27180A7C00E3DDCF /* UpIsGrayModeAction.m in Sources */,
				1476EE852715283100D3EC80 /* UpGetPhoneInfoAction.m in Sources */,
				5EB130D92B3AAAA90004103D /* UpFunctionToggleAction.swift in Sources */,
				14742ECC2716667F0006B631 /* UpAppinfoPluginManager.m in Sources */,
				14640471274B704D00E1A37B /* UpGetCacheFilesSizeAction.m in Sources */,
				145B75552717C86C00E3DDCF /* UpGetAppInfoAction.m in Sources */,
				145B756127180E7B00E3DDCF /* UpUpdateGrayModeAction.m in Sources */,
				14640475274B706200E1A37B /* UpCleanCacheFilesAction.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		14B9B21127315BB7004290AA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				14B9B22D27315DD1004290AA /* UpAppinfoPluginIMP.m in Sources */,
				14B9B22C27315DD1004290AA /* UpAppinfoUtils.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		145B7597271D3F5800E3DDCF /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 1476EE5D271523EE00D3EC80 /* UpAppinfoPlugin */;
			targetProxy = 145B7596271D3F5800E3DDCF /* PBXContainerItemProxy */;
		};
		14B9B23127315FD0004290AA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 14B9B21427315BB7004290AA /* UpAppinfoPluginFramework */;
			targetProxy = 14B9B23027315FD0004290AA /* PBXContainerItemProxy */;
		};
		14B9B2332731617E004290AA /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 14B9B21427315BB7004290AA /* UpAppinfoPluginFramework */;
			targetProxy = 14B9B2322731617E004290AA /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		1465B1E527265551006D9FF0 /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1465B1E627265551006D9FF0 /* Base */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
		1465B1EA27265553006D9FF0 /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				1465B1EB27265553006D9FF0 /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		145B7599271D3F5800E3DDCF /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = B12B925F6282478A38BC0DE7 /* Pods-UpAppinfoPluginTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpAppinfoPluginTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		145B759A271D3F5800E3DDCF /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = D51D0B01051BC4D7538227BB /* Pods-UpAppinfoPluginTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpAppinfoPluginTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1465B1F027265553006D9FF0 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 508ADEA2E1219BEDB58DE614 /* Pods-UpAppinfoPluginDebuger.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = UpAppinfoPluginDebuger/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpAppinfoPluginDebuger;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1465B1F127265553006D9FF0 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 7BFD3AC02D49EDA534B42BF1 /* Pods-UpAppinfoPluginDebuger.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = UpAppinfoPluginDebuger/Info.plist;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpAppinfoPluginDebuger;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		1476EE63271523EE00D3EC80 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Debug;
		};
		1476EE64271523EE00D3EC80 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
				VERSIONING_SYSTEM = "apple-generic";
				VERSION_INFO_PREFIX = "";
			};
			name = Release;
		};
		1476EE66271523EE00D3EC80 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 0BF0A55F3E7FEF0DF828C4D4 /* Pods-UpAppinfoPlugin.debug.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpAppinfoPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1476EE67271523EE00D3EC80 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 4EF7B7E10AA43FBA6BC9ABEA /* Pods-UpAppinfoPlugin.release.xcconfig */;
			buildSettings = {
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpAppinfoPlugin;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_INSTALL_OBJC_HEADER = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		14B9B21927315BB7004290AA /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1D148A4563E3A55965E51E0C /* Pods-UpAppinfoPluginFramework.debug.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LINK_WITH_STANDARD_LIBRARIES = YES;
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				ONLY_ACTIVE_ARCH = YES;
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpAppinfoPluginFramework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		14B9B21A27315BB7004290AA /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 61006F51E72248EF8225B598 /* Pods-UpAppinfoPluginFramework.release.xcconfig */;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEFINES_MODULE = YES;
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				DYLIB_INSTALL_NAME_BASE = "@rpath";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				INSTALL_PATH = "$(LOCAL_LIBRARY_DIR)/Frameworks";
				IPHONEOS_DEPLOYMENT_TARGET = 10.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				LINK_WITH_STANDARD_LIBRARIES = YES;
				MACH_O_TYPE = staticlib;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.uhome.haier.Uplus.UpAppinfoPluginFramework;
				PRODUCT_NAME = "$(TARGET_NAME:c99extidentifier)";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		145B7598271D3F5800E3DDCF /* Build configuration list for PBXNativeTarget "UpAppinfoPluginTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				145B7599271D3F5800E3DDCF /* Debug */,
				145B759A271D3F5800E3DDCF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1465B1F227265553006D9FF0 /* Build configuration list for PBXNativeTarget "UpAppinfoPluginDebuger" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1465B1F027265553006D9FF0 /* Debug */,
				1465B1F127265553006D9FF0 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1476EE58271523EE00D3EC80 /* Build configuration list for PBXProject "UpAppinfoPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1476EE63271523EE00D3EC80 /* Debug */,
				1476EE64271523EE00D3EC80 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1476EE65271523EE00D3EC80 /* Build configuration list for PBXNativeTarget "UpAppinfoPlugin" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1476EE66271523EE00D3EC80 /* Debug */,
				1476EE67271523EE00D3EC80 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		14B9B21B27315BB7004290AA /* Build configuration list for PBXNativeTarget "UpAppinfoPluginFramework" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				14B9B21927315BB7004290AA /* Debug */,
				14B9B21A27315BB7004290AA /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1476EE55271523EE00D3EC80 /* Project object */;
}
