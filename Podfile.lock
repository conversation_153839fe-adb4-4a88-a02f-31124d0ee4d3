PODS:
  - AFNetworking (4.0.1):
    - AFNetworking/NSURLSession (= 4.0.1)
    - AFNetworking/Reachability (= 4.0.1)
    - AFNetworking/Security (= 4.0.1)
    - AFNetworking/Serialization (= 4.0.1)
    - AFNetworking/UIKit (= 4.0.1)
  - AFNetworking/NSURLSession (4.0.1):
    - AFNetworking/Reachability
    - AFNetworking/Security
    - AFNetworking/Serialization
  - AFNetworking/Reachability (4.0.1)
  - AFNetworking/Security (4.0.1)
  - AFNetworking/Serialization (4.0.1)
  - AFNetworking/UIKit (4.0.1):
    - AFNetworking/NSURLSession
  - AWFileHash (0.2.0)
  - Cucumberish (1.4.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/SQLCipher (2.7.5):
    - SQLCipher
  - FMDB/standard (2.7.5)
  - Godzip (1.0.0)
  - MJExtension (3.2.1)
  - OCHamcrest (7.1.2)
  - OCMock (3.8.1)
  - Protobuf (3.17.0)
  - Realm (10.28.3):
    - Realm/Headers (= 10.28.3)
  - Realm/Headers (10.28.3)
  - SQLCipher (4.5.3):
    - SQLCipher/standard (= 4.5.3)
  - SQLCipher/common (4.5.3)
  - SQLCipher/standard (4.5.3):
    - SQLCipher/common
  - UHMasonry (1.1.2.2023060801)
  - UPCache (1.1.0.2238.2022101401):
    - UPCore/CoreHive (>= 3.1.4)
    - UPResource (>= 2.15.4)
    - UPStorage (>= 1.4.11)
    - upuserdomain (>= 3.0.4)
  - UPCore/CoreHive (3.5.9.2023120401):
    - UPCore/LaunchTime
    - UPCore/UPContext
    - UpTrace/UpTrace
    - UpTrace/UpTraceCore
  - UPCore/LaunchTime (3.5.9.2023120401):
    - YYModel
  - UPCore/toggles (3.5.9.2023120401)
  - UPCore/UPContext (3.5.9.2023120401):
    - UPStorage (>= 1.4.13)
    - YYModel
  - uplog (1.4.0):
    - AFNetworking (>= 4.0.1)
    - Protobuf (= 3.17.0)
    - ZipArchive (>= 1.4.0)
  - upnetwork (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign (= 4.0.6)
    - upnetwork/Headers (= 4.0.6)
    - upnetwork/HTTPDns (= 4.0.6)
    - upnetwork/Manager (= 4.0.6)
    - upnetwork/Request (= 4.0.6)
    - upnetwork/Settings (= 4.0.6)
    - upnetwork/Utils (= 4.0.6)
  - upnetwork/DynamicSign (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/DynamicSign/Private (= 4.0.6)
  - upnetwork/DynamicSign/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Headers (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Headers/Private (= 4.0.6)
  - upnetwork/Headers/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/HTTPDns (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Manager (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Manager/Private (= 4.0.6)
  - upnetwork/Manager/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Request (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Request/Private (= 4.0.6)
  - upnetwork/Request/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Settings (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Settings/Private (= 4.0.6)
  - upnetwork/Settings/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - upnetwork/Utils (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
    - upnetwork/Utils/Private (= 4.0.6)
  - upnetwork/Utils/Private (4.0.6):
    - AFNetworking (= 4.0.1)
    - MJExtension (>= ********)
    - uplog (>= 1.1.8)
  - UPPluginBaseAPI (0.1.3):
    - UPPluginBaseAPI/UPPluginBaseAPI (= 0.1.3)
  - UPPluginBaseAPI/UPPluginBaseAPI (0.1.3)
  - UpPluginFoundation (********.2023020301):
    - MJExtension
    - uplog (>= 1.1.22)
    - UPPluginBaseAPI (>= 0.1.0)
  - UPResource (********.2022041402):
    - AWFileHash (>= 0.1.0)
    - FMDB (>= 2.7.5)
    - uplog (>= 1.1.8)
    - upnetwork (>= 4.0.0)
    - UPResource/UPRes (= ********.2022041402)
    - UPResource/UPResDelegateIMP (= ********.2022041402)
    - UPTools/ModuleLanguage (>= 0.1.13)
    - ZipArchive (>= 1.4.0)
  - UPResource/UPRes (********.2022041402):
    - AWFileHash (>= 0.1.0)
    - FMDB (>= 2.7.5)
    - uplog (>= 1.1.8)
    - upnetwork (>= 4.0.0)
    - UPTools/ModuleLanguage (>= 0.1.13)
    - ZipArchive (>= 1.4.0)
  - UPResource/UPResDelegateIMP (********.2022041402):
    - AWFileHash (>= 0.1.0)
    - FMDB (>= 2.7.5)
    - uplog (>= 1.1.8)
    - upnetwork (>= 4.0.0)
    - UPTools/ModuleLanguage (>= 0.1.13)
    - ZipArchive (>= 1.4.0)
  - UPStorage (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
    - UPStorage/Common (= 1.4.15)
    - UPStorage/DataChange (= 1.4.15)
    - UPStorage/Manager (= 1.4.15)
    - UPStorage/Private (= 1.4.15)
    - UPStorage/Public (= 1.4.15)
    - UPStorage/Storage (= 1.4.15)
    - UPStorage/UPStorageUtil (= 1.4.15)
  - UPStorage/Common (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/DataChange (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Manager (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Private (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Public (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/Storage (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPStorage/UPStorageUtil (1.4.15):
    - FMDB/SQLCipher (>= 2.7.5)
    - uplog (>= 1.1.12)
  - UPTools/ModuleLanguage (0.2.4.2023112701):
    - AFNetworking (>= 3.1.0)
    - UHMasonry (>= 1.1.1)
  - UpTrace/UpTrace (1.3.2.2023102101):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
  - UpTrace/UpTraceCore (1.3.2.2023102101):
    - Godzip (= 1.0.0)
    - MJExtension (>= 3.2.1)
    - Realm (= 10.28.3)
    - uplog (>= 1.4.0)
    - upnetwork (>= 4.0.6)
  - upuserdomain (3.20.0.2023101201):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
    - upuserdomain/upuserdomain (= 3.20.0.2023101201)
    - upuserdomain/UserDomainAPIs (= 3.20.0.2023101201)
    - upuserdomain/UserDomainDataSource (= 3.20.0.2023101201)
  - upuserdomain/upuserdomain (3.20.0.2023101201):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainAPIs (3.20.0.2023101201):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - upuserdomain/UserDomainDataSource (3.20.0.2023101201):
    - MJExtension (= 3.2.1)
    - uplog (>= 1.1.8)
    - upnetwork (>= 3.2.1)
  - UPVDN (*******.2023032102):
    - uplog (>= 1.1.2)
    - UPVDN/Back (= *******.2023032102)
    - UPVDN/Categorys (= *******.2023032102)
    - UPVDN/Launcher (= *******.2023032102)
    - UPVDN/Page (= *******.2023032102)
    - UPVDN/Patch (= *******.2023032102)
    - UPVDN/ResultListener (= *******.2023032102)
    - UPVDN/Utils (= *******.2023032102)
    - UPVDN/VDNManager (= *******.2023032102)
    - UPVDN/Vdns (= *******.2023032102)
    - UPVDN/VirtualDomain (= *******.2023032102)
  - UPVDN/Back (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Categorys (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Launcher (*******.2023032102):
    - uplog (>= 1.1.2)
    - UPVDN/Launcher/Native (= *******.2023032102)
  - UPVDN/Launcher/Native (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Page (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Patch (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/ResultListener (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Utils (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/VDNManager (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/Vdns (*******.2023032102):
    - uplog (>= 1.1.2)
  - UPVDN/VirtualDomain (*******.2023032102):
    - uplog (>= 1.1.2)
  - YYModel (1.0.4)
  - ZipArchive (1.4.0)

DEPENDENCIES:
  - Cucumberish (= 1.4.0)
  - OCHamcrest (~> 7.1.2)
  - OCMock (= 3.8.1)
  - UPCache (= 1.1.0.2238.2022101401)
  - UPCore/toggles (= 3.5.9.2023120401)
  - UPCore/UPContext (= 3.5.9.2023120401)
  - uplog (= 1.4.0)
  - upnetwork (= 4.0.6)
  - UPPluginBaseAPI (= 0.1.3)
  - UpPluginFoundation (= ********.2023020301)
  - UPResource (= ********.2022041402)
  - UPStorage (= 1.4.15)
  - upuserdomain (= 3.20.0.2023101201)
  - UPVDN (= *******.2023032102)

SPEC REPOS:
  https://git.haier.net/uplus/shell/cocoapods/Specs.git:
    - UHMasonry
    - UPCache
    - UPCore
    - uplog
    - upnetwork
    - UPPluginBaseAPI
    - UpPluginFoundation
    - UPResource
    - UPStorage
    - UPTools
    - UpTrace
    - upuserdomain
    - UPVDN
  https://github.com/CocoaPods/Specs.git:
    - AFNetworking
    - AWFileHash
    - Cucumberish
    - FMDB
    - Godzip
    - MJExtension
    - OCHamcrest
    - OCMock
    - Protobuf
    - Realm
    - SQLCipher
    - YYModel
    - ZipArchive

SPEC CHECKSUMS:
  AFNetworking: 3bd23d814e976cd148d7d44c3ab78017b744cd58
  AWFileHash: 5b0d1f458a1b8d4de636f12f2d71039494899521
  Cucumberish: 6cbd0c1f50306b369acebfe7d9f514c9c287d26c
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  Godzip: 4ee041500d1b0d56aa2415af3d99b932bfdec007
  MJExtension: 635f2c663dcb1bf76fa4b715b2570a5710aec545
  OCHamcrest: b284c9592c28c1e4025a8542e67ea41a635d0d73
  OCMock: 29f6e52085b4e7d9b075cbf03ed7c3112f82f934
  Protobuf: 7327d4444215b5f18e560a97f879ff5503c4581c
  Realm: 64e66568d981de2496f81ecab2e1372b27dc7d58
  SQLCipher: 57fa9f863fa4a3ed9dd3c90ace52315db8c0fdca
  UHMasonry: 3be12fd6fbbb52fad07a26b4e0a8c667c9fd24f4
  UPCache: 23b17bd6320a7401dd138a69498e30a315edc8d5
  UPCore: d9d4d9383d65641a67e6320c82b9913ac4051ece
  uplog: 6b1fadd6ba73b2a4625bbf4fdfc362d396763ae6
  upnetwork: 5209ebffb17785e0aafd7344888908f702231c68
  UPPluginBaseAPI: 0aca03a1616948abc0d0000dea46b6ee1b4d3d57
  UpPluginFoundation: 51455f69d894ea83df4f60948c739b0933be8932
  UPResource: fdc9c4fbf4bbfea1a8040cdc7ec8747bba87017b
  UPStorage: 6215fb97913b2229900ea9d59997a660e6c45487
  UPTools: 0d5605e6efca669ae99ea77132afa25a16469a8a
  UpTrace: 18e43ddc5c282ba7c15a5a78699a5dab13fd4be6
  upuserdomain: 7e2e6304fa9fd45173c031c627f0bd570948ce84
  UPVDN: cf8f1246c0af89bae2f5b7191880a177f1319d8e
  YYModel: 2a7fdd96aaa4b86a824e26d0c517de8928c04b30
  ZipArchive: e25a4373192673e3229ac8d6e9f64a3e5713c966

PODFILE CHECKSUM: d8baa81c2e37b3eb2995670d8c6ab9c6446b3736

COCOAPODS: 1.16.2
