//
//  UpCleanCacheFilesAction.m
//  UpAppinfoPlugin
//
//  Created by yaos<PERSON>u on 2021/11/22.
//

#import "UpCleanCacheFilesAction.h"
#import "UpAppinfoPluginManager.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import <UPCore/UPContext.h>

NSString *const CleanCacheFiles_Action = @"cleanCacheForAppInfo";

@implementation UpCleanCacheFilesAction

+ (NSString *)action
{
    return CleanCacheFiles_Action;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{

    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
      // 在后台线程中执行耗时操作或计算
      [[UpAppinfoPluginManager sharedInstance]
              .appinfoMethodDelegate cleanCacheFiles:^{

        dispatch_async(dispatch_get_main_queue(), ^{
          UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功"];
          [callback onSuccess:[kUPCommonResult toJsonObject]];
        });

      }];

    });
}

@end
