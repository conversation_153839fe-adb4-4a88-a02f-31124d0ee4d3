//
//  UpGetDeviceModelAction.m
//  UpAppinfoPlugin
//
//  Created by Yo Civic on 2021/10/12.
//

#import "UpGetPhoneInfoAction.h"
#import "UpAppinfoPluginManager.h"
#import <UpPluginFoundation/UpPluginFoundation.h>

NSString *const GetDeviceModel_ActionName = @"getPhoneInfoForAppInfo";

@implementation UpGetPhoneInfoAction

+ (NSString *)action
{
    return GetDeviceModel_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSString *deviceIdentifier = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceIdentifier];
    NSString *deviceModel = [self currentDeviceModel:deviceIdentifier];
    NSDictionary *deviceDpi = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceDpi];
    NSDictionary *screenSize = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getPhoneScreenSize];
    NSDictionary *safeArea = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getPhoneSafeArea];
    NSDictionary *dic = @{ @"model" : deviceModel,
                           @"dpi" : deviceDpi,
                           @"brand" : @"apple",
                           @"size" : screenSize,
                           @"safeArea" : safeArea };
    UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功" withData:dic];
    [callback onSuccess:[kUPCommonResult toJsonObject]];
}

- (NSString *)currentDeviceModel:(NSString *)deviceIdentifier
{
    NSBundle *bundle = [NSBundle bundleForClass:[self class]];
    NSString *filePath = [bundle pathForResource:@"DeviceModels" ofType:@"plist"];
    NSDictionary *dics = [NSDictionary dictionaryWithContentsOfFile:filePath];

    NSString *deviceModel = dics[deviceIdentifier];
    if (nil == deviceModel) {
        deviceModel = deviceIdentifier;
    }
    return deviceModel;
}

@end
