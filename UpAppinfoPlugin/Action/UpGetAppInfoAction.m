//
//  UpGetAppInfoAction.m
//  UpAppinfoPlugin
//
//  Created by yaos<PERSON><PERSON> on 2021/10/14.
//

#import "UpGetAppInfoAction.h"
#import "UpAppinfoPluginManager.h"
#import <UpPluginFoundation/UpPluginFoundation.h>

NSString *const GetAppInfo_ActionName = @"getAppInfoForAppInfo";

@implementation UpGetAppInfoAction

+ (NSString *)action
{
    return GetAppInfo_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    NSDictionary *appInfo = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getAppInfo];
    UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功" withData:appInfo];
    [callback onSuccess:[kUPCommonResult toJsonObject]];
}


@end
