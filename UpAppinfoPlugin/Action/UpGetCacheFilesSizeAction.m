//
//  UpGetCacheFilesSizeAction.m
//  UpAppinfoPlugin
//
//  Created by ya<PERSON><PERSON><PERSON> on 2021/11/22.
//

#import "UpGetCacheFilesSizeAction.h"
#import "UpAppinfoPluginManager.h"
#import <UpPluginFoundation/UpPluginFoundation.h>

NSString *const GetCacheFileSize_ActionName = @"getCacheSizeForAppInfo";

@implementation UpGetCacheFilesSizeAction

+ (NSString *)action
{
    return GetCacheFileSize_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    [[UpAppinfoPluginManager sharedInstance]
            .appinfoMethodDelegate getCacheFilesSize:^(unsigned long long fileSize) {
      UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功" withData:@(fileSize)];
      [callback onSuccess:[kUPCommonResult toJsonObject]];
    }];
}

@end
