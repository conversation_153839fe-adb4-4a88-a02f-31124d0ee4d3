//
//  UpIsGrayModeAction.m
//  UpAppinfoPlugin
//
//  Created by yaos<PERSON>u on 2021/10/14.
//

#import "UpIsGrayModeAction.h"
#import "UpAppinfoPluginManager.h"
#import <UpPluginFoundation/UpPluginFoundation.h>

NSString *const IsGrayMode_ActionName = @"isGrayModeForAppInfo";

@implementation UpIsGrayModeAction

+ (NSString *)action
{
    return IsGrayMode_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    BOOL grayMode = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate isGrayMode];
    UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功" withData:@(grayMode)];
    [callback onSuccess:[kUPCommonResult toJsonObject]];
}


@end
