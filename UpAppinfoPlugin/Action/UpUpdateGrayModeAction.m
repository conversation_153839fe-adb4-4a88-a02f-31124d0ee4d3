//
//  UpUpdateGrayModeAction.m
//  UpAppinfoPlugin
//
//  Created by yaos<PERSON>u on 2021/10/14.
//


#import "UpUpdateGrayModeAction.h"
#import "UpAppinfoPluginManager.h"
#import <UpPluginFoundation/UpPluginFoundation.h>

NSString *const UpdateGrayMode_ActionName = @"setGrayModeForAppInfo";

@implementation UpUpdateGrayModeAction

+ (NSString *)action
{
    return UpdateGrayMode_ActionName;
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback
{
    if (nil == params || 0 == params.allKeys.count) {
        NSString *errorInfo = @"参数错误";
        if (nil == params) {
            errorInfo = @"参数错误(null)";
        }
        else if (0 == params.allKeys.count) {
            errorInfo = @"参数错误({})";
        }
        UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"900003" retInfo:errorInfo];
        [callback onFailure:@"900003" errMessage:errorInfo details:[kUPCommonResult toJsonObject]];
        return;
    }

    BOOL grayMode = NO;
    if (nil != params[@"grayMode"]) {
        grayMode = [params[@"grayMode"] boolValue];
    }
    [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate setGrayMode:grayMode];
    UPCommonResult *kUPCommonResult = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功"];
    [callback onSuccess:[kUPCommonResult toJsonObject]];
}

@end
