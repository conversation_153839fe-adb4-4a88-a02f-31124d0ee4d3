//
//  UpFunctionToggleAction.swift
//  UpAppinfoPlugin
//
//  Created by ByteBai on 2023/12/26.
//

import Foundation
import UpPluginFoundation

let FunctionToggleActionName = "FunctionToggleActionForAppInfo"

@objcMembers
public class UpFunctionToggleAction: UpPluginAction {

    public override class var action: String {
        return FunctionToggleActionName
    }

    @objc public override func execute(_ action: String, params: [AnyHashable : Any], options: Any?, finishBlock callback: UPPCallBackProtocol?) {
        
        if let key = params["key"] as? String, !key.isEmpty {
            let retData = UpAppinfoPluginManager.sharedInstance().appinfoMethodDelegate.getToggleInfo(key)
            let result = UPCommonResult(successRetData: retData)
            callback?.onSuccess(result.toJsonObject())
        } else {
            let result = UPCommonResult.parameterError()
            callback?.onFailure(result.retCode, errMessage: result.retCode, details:result.toJsonObject());
        }
    }
}
