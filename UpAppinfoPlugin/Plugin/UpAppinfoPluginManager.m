//
//  UpAppinfoPluginManager.m
//  UpAppinfoPlugin
//
//  Created by Yo Civic on 2021/10/13.
//

#import "UpAppinfoPluginManager.h"
#import "UpAppinfoPluginIMP.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpGetPhoneInfoAction.h"
#import "UpGetAppInfoAction.h"
#import "UpIsGrayModeAction.h"
#import "UpUpdateGrayModeAction.h"
#import "UpGetCacheFilesSizeAction.h"
#import "UpCleanCacheFilesAction.h"
#import "UpAppinfoPlugin-Swift.h"

static UpAppinfoPluginManager *manager = nil;
@implementation UpAppinfoPluginManager

+ (UpAppinfoPluginManager *)sharedInstance
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
      manager = UpAppinfoPluginManager.new;
    });
    return manager;
}

+ (void)load
{
    NSArray<Class<UpPluginActionProtocol>> *actions = @[
        UpGetPhoneInfoAction.class,
        UpGetAppInfoAction.class,
        UpIsGrayModeAction.class,
        UpUpdateGrayModeAction.class,
        UpGetCacheFilesSizeAction.class,
        UpCleanCacheFilesAction.class,
        UpFunctionToggleAction.class
    ];
    UpPluginActionManager *manager = [UpPluginActionManager sharedInstance];
    [actions enumerateObjectsUsingBlock:^(Class<UpPluginActionProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
      [manager appendAction:[obj action] creator:obj];
    }];
}

- (id<UpAppInfoDelegate>)appinfoMethodDelegate
{
    return _appinfoMethodDelegate ? _appinfoMethodDelegate : UpAppinfoPluginIMP.new;
}

@end
