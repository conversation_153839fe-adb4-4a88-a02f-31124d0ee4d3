#
# Be sure to run `pod lib lint UpLocationPlugin.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#


Pod::Spec.new do |s|
  s.name             = 'UpAppinfoPlugin'
  s.version          = '0.1.0'
  s.summary          = 'UpAppinfoPlugin 插件库'
  s.description      = <<-DESC
  UpAppinfoPlugin 插件库
                       DESC

  s.homepage         = 'https://gitlab.haier.net/uplus/ios/plugins/upappinfoplugin'
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = '海尔优家智能科技（北京）有限公司'
  s.source           = { :git => 'https://git.haier.net/uplus/ios/plugins/upappinfoplugin.git', :tag => s.version.to_s }
  s.requires_arc    = true
  s.frameworks      = 'Foundation'
  s.module_name     = 'UpAppinfoPlugin'
  s.ios.deployment_target = '10.0'
  s.resources = 'UpAppinfoPlugin/Plugin/DeviceModels.plist'
  s.source_files = 'UpAppinfoPlugin/*.{h,m,mm,cpp,c,cc,swift}', 'UpAppinfoPlugin/**/*.{h,m,mm,cpp,c,cc,swift}', 'UpAppinfoPluginFramework/*.{h,m,mm,cpp,c,cc}', 'UpAppinfoPluginFramework/**/*.{h,m,mm,cpp,c,cc}'
  s.dependency 'UpPluginFoundation', '>=0.1.12'
  s.dependency 'UPPluginBaseAPI', '>=0.1.1'
  s.dependency 'uplog','>=1.1.27'
  s.dependency 'UPCore/UPContext','>=3.5.3'
  s.dependency 'upnetwork','>=4.0.2'
  s.dependency 'UPVDN','>=2.5.17'
  s.dependency 'UPResource','>=2.15.17'
  s.dependency 'UPCache','>=1.0.5.1.**********'
  
  s.pod_target_xcconfig = {
      "DEFINES_MODULE" => "YES"
  }

end
