//
//  UpAppinfoPluginFramework.h
//  UpAppinfoPluginFramework
//
//  Created by yaos<PERSON>u on 2021/11/2.
//

#import <Foundation/Foundation.h>

//! Project version number for UpAppinfoPluginFramework.
FOUNDATION_EXPORT double UpAppinfoPluginFrameworkVersionNumber;

//! Project version string for UpAppinfoPluginFramework.
FOUNDATION_EXPORT const unsigned char UpAppinfoPluginFrameworkVersionString[];

// In this header, you should import all the public headers of your framework using statements like #import <UpAppinfoPluginFramework/PublicHeader.h>
#import "UpAppinfoPluginIMP.h"
