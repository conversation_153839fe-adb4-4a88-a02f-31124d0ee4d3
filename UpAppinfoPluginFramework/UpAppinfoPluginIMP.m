//
//  UpAppinfoPluginIMP.m
//  UpAppinfoPlugin
//
//  Created by ya<PERSON><PERSON><PERSON> on 2021/10/14.
//

#import "UpAppinfoPluginIMP.h"
#import <UPCore/UPContext.h>
#import <sys/utsname.h>
#import "UpAppinfoUtils.h"
#import <upnetwork/UPNetworkSettings.h>
#import <UPCache/CHaierCleanCacheTool.h>
#import <UPStorage/UPStorage.h>
#import <UPCore/UPFunctionToggle.h>

NSString *const kAppConfigGrayModeKey = @"kUPDeviceTestState";

@interface UpAppinfoPluginIMP () <UPResCacheCleanCallback>

@end

@implementation UpAppinfoPluginIMP {
    CleanFinishCallback _callback;
    BOOL _isSyncCleanFinish; //标记同步清除缓存是否完成
    BOOL _isAsyncCleanFinish; //标记异步清除缓存是否完成
}

- (NSString *)getDeviceIdentifier
{
    struct utsname systemInfo;
    uname(&systemInfo);

    NSString *deviceIdentifier = [NSString stringWithCString:systemInfo.machine encoding:NSASCIIStringEncoding];
    return deviceIdentifier ?: @"unknown";
}

- (NSDictionary *)getDeviceDpi
{
    CGFloat screenScale = [UIScreen mainScreen].scale;
    CGFloat screenX = [UIScreen mainScreen].bounds.size.width * screenScale;
    CGFloat screenY = [UIScreen mainScreen].bounds.size.height * screenScale;
    return @{
        @"x" : @(screenX),
        @"y" : @(screenY)
    };
}

- (NSDictionary *)getPhoneScreenSize
{
    CGSize screenSize = [UIScreen mainScreen].bounds.size;
    return @{
        @"width" : @(screenSize.width),
        @"height" : @(screenSize.height)
    };
}

- (NSDictionary *)getPhoneSafeArea
{
    UIEdgeInsets safeArea = UIEdgeInsetsMake(20, 0, 0, 0);
    if (@available(iOS 11.0, *)) {
        safeArea = [UIApplication sharedApplication].keyWindow.safeAreaInsets;
    }
    return @{
        @"top" : @(safeArea.top),
        @"bottom" : @(safeArea.bottom)
    };
}

- (id)getToggleInfo:(NSString *)key
{
    return [UPFunctionToggle.shareInstance toggleValueForKey:key];
}

- (NSDictionary *)getAppInfo
{
    NSString *appId = [UPNetworkSettings sharedSettings].appID;
    NSString *appKey = [UPNetworkSettings sharedSettings].appKey;
    NSString *haierUserCenterUrl = UPContext.sharedInstance.haierUserCenterUrl ?: @"";
    NSString *haierClientId = UPContext.sharedInstance.haierClientId ?: @"";
    NSString *haierClientSecret = UPContext.sharedInstance.haierClientSecret ?: @"";

    NSString *appVersion = [UPContext sharedInstance].appVersion ?: @"";
    NSString *buildVersion = [UPStorage getStringValue:@"kAppBuildVersion" defaultValue:@"0"];
    NSString *clientId = [UPNetworkSettings sharedSettings].clientID;

    BOOL isGuest = [UPStorage getBooleanValue:UPIsGuestMode defaultValue:NO];
    NSString *idfaStr = @"";
    if (!isGuest) {
        idfaStr = [UPContext sharedInstance].appIdfa ?: @""; //设备IDFA，包含“-”
    }
    NSNumber *grayMode = [[NSUserDefaults standardUserDefaults] objectForKey:@"kUPDeviceTestState"];
    NSString *systemVersion = @"";
    if (!isGuest) {
        systemVersion = getSystemVersion();
    }
    NSNumber *testMode = [[NSUserDefaults standardUserDefaults] objectForKey:@"kUPTestState"];
    //用于三翼鸟判断是否是预发环境
    BOOL preViewEnv = [UPContext sharedInstance].isPreViewEnv;

    NSString *env = @"";
    switch (([UPContext sharedInstance].env)) {
        case UPEnvironmentDev:
            env = @"联调";
            break;
        case UPEnvironmentAcceptance:
            env = @"验收";
            break;
        case UPEnvironmentVerify:
            env = @"验证";
            break;
        case UPEnvironmentProd:
            env = @"生产";
            break;
        case UPEnvironmentDebug:
            env = @"调试";
            break;
    }

    NSDictionary *dic = @{
        @"appId" : appId,
        @"env" : env,
        @"idfa" : idfaStr,
        @"appKey" : appKey,
        @"testMode" : testMode ? testMode : [NSNumber numberWithBool:false],
        @"haierUserCenterUrl" : haierUserCenterUrl,
        @"haierClientId" : haierClientId,
        @"platform" : @"iOS",
        @"grayMode" : grayMode ? grayMode : [NSNumber numberWithBool:false],
        @"OSversion" : systemVersion,
        @"appVersion" : appVersion,
        @"versionCode" : @([buildVersion integerValue]),
        @"clientId" : clientId,
        @"haierClientSecret" : haierClientSecret,
        @"isPreViewEnv" : @(preViewEnv)
    };
    return dic;
}

- (BOOL)isGrayMode
{
    BOOL grayMode = NO;
    NSNumber *grayModeNumber = [[NSUserDefaults standardUserDefaults] valueForKey:kAppConfigGrayModeKey];
    if ([grayModeNumber isKindOfClass:[NSNumber class]]) {
        grayMode = [grayModeNumber boolValue];
    }
    return grayMode;
}

- (void)setGrayMode:(BOOL)grayMode
{
    [[NSUserDefaults standardUserDefaults] setObject:@(grayMode) forKey:kAppConfigGrayModeKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}

- (void)getCacheFilesSize:(void (^)(unsigned long long fileSize))callback
{
    [CHaierCleanCacheTool getCacheSizeFromDocumentAndCahche:^(unsigned long long cachesize) {
      callback(cachesize);
    }];
}

- (void)cleanCacheFiles:(CleanFinishCallback)callBack
{
    if (nil == [UPResourceInjection getInstance]) {
        if (nil == [UPResourceConfig shareInstance].appVersion) {
            [UPResourceConfig shareInstance].appVersion = [UPContext sharedInstance].appVersion;
        }
        UPResourceAppPlatform platform = UPResourceAppPlatformChina;
        if ([[UPContext sharedInstance].appID isEqualToString:@"MB-UZHSH-0001"]) {
            platform = UPResourceAppPlatformChina;
        }
        else if ([[UPContext sharedInstance].appID isEqualToString:@"MB-SHEYJDNYB-0000"]) {
            platform = UPResourceAppPlatformSoutheasAsia;
        }
        else {
            platform = UPResourceAppPlatformChina;
        }

        [UPResourceInjection initializeWithTestModeOn:[UPContext sharedInstance].isGrayscaleMode appPlatform:platform];
    }

    _callback = callBack;
    CHaierCleanCacheTool.cleanCallBack = self;
    [CHaierCleanCacheTool clearCacheForDocumentAndCache];
    _isSyncCleanFinish = YES;
    if (YES == _isAsyncCleanFinish && nil != _callback) {
        _callback();
    }
}

- (void)asyncCleanCacheDone
{
    _isAsyncCleanFinish = YES;
    if (YES == _isSyncCleanFinish && nil != _callback) {
        _callback();
    }
}

#pragma mark - UPResCacheCleanCallback
- (void)didFinishCacheCleaningWithOptions:(NSDictionary *)options
{
    [self asyncCleanCacheDone];
}

- (void)resourceCacheDidFailCleanWithError:(NSError *)error
{
    [self asyncCleanCacheDone];
}

@end
