//
//  UpAppinfoUtils.m
//  UpAppinfoPlugin
//
//  Created by yaosixu on 2021/10/14.
//

#import "UpAppinfoUtils.h"
#import <UIKit/UIKit.h>


NSString *formatSystemVersion(NSString *systemVersion)
{
    NSString *newVersionInfo = @"";
    NSArray *components = [systemVersion componentsSeparatedByString:@"."];
    for (int i = 0; i < 3; i++) {
        NSString *obj;
        if (i < components.count) {
            obj = [components objectAtIndex:i];
        }
        if (!obj || [obj isEqualToString:@""]) {
            obj = @"0";
        }
        newVersionInfo = [newVersionInfo stringByAppendingFormat:@"%@.", obj];
    }
    newVersionInfo = [newVersionInfo substringToIndex:newVersionInfo.length - 1];
    return newVersionInfo;
}

NSString *getSystemVersion(void)
{
    NSString *systemVersion = [[UIDevice currentDevice] systemVersion];
    if (![systemVersion isKindOfClass:NSString.class]) {
        systemVersion = @"0.0.0";
        return systemVersion;
    }
    return formatSystemVersion(systemVersion);
}
