//
//  UpAppInfoDelegate.h
//  UpAppinfoPlugin
//
//  Created by yaosixu on 2021/10/14.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void (^CleanFinishCallback)(void);

@protocol UpAppInfoDelegate <NSObject>

/// 获取设备型号标识
- (NSString *)getDeviceIdentifier;

/// 获取App信息
- (NSDictionary *)getAppInfo;

/// 获取toggle开关信息
- (id)getToggleInfo:(NSString *)key;

/// 获取设备宽高像素值
- (NSDictionary *)getDeviceDpi;

/// 获取手机屏幕尺寸
- (NSDictionary *)getPhoneScreenSize;

/// 获取手机安全区域
- (NSDictionary *)getPhoneSafeArea;

/// 获取灰度状态
- (BOOL)isGrayMode;

/// 更新灰度状态
/// @param grayMode 新的灰度状态
- (void)setGrayMode:(BOOL)grayMode;

/// 获取缓存文件大小
- (void)getCacheFilesSize:(void (^)(unsigned long long fileSize))callback;

/// 清除缓存文件
- (void)cleanCacheFiles:(CleanFinishCallback)callBack;

@end

NS_ASSUME_NONNULL_END
