# UpAppinfoPlugin 整体架构文档

## 架构概览

UpAppinfoPlugin 采用分层架构设计，基于 U+ 插件框架构建，提供应用信息获取和管理功能。

## 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        Flutter[Flutter App]
        H5[H5 App]
        Native[Native App]
    end
    
    subgraph "插件接口层"
        PluginManager[UpAppinfoPluginManager]
        ActionManager[UpPluginActionManager]
    end
    
    subgraph "业务逻辑层"
        GetPhoneInfo[UpGetPhoneInfoAction]
        GetAppInfo[UpGetAppInfoAction]
        GrayMode[UpIsGrayModeAction/UpUpdateGrayModeAction]
        Cache[UpGetCacheFilesSizeAction/UpCleanCacheFilesAction]
        Toggle[UpFunctionToggleAction]
    end
    
    subgraph "框架实现层"
        Delegate[UpAppInfoDelegate]
        Implementation[UpAppinfoPluginIMP]
        Utils[UpAppinfoUtils]
    end
    
    subgraph "系统依赖层"
        UPCore[UPCore/UPContext]
        UPCache[UPCache]
        UPStorage[UPStorage]
        UPNetwork[upnetwork]
        Foundation[iOS Foundation]
    end
    
    Flutter --> PluginManager
    H5 --> PluginManager
    Native --> PluginManager
    
    PluginManager --> ActionManager
    ActionManager --> GetPhoneInfo
    ActionManager --> GetAppInfo
    ActionManager --> GrayMode
    ActionManager --> Cache
    ActionManager --> Toggle
    
    GetPhoneInfo --> Delegate
    GetAppInfo --> Delegate
    GrayMode --> Delegate
    Cache --> Delegate
    Toggle --> Delegate
    
    Delegate --> Implementation
    Implementation --> Utils
    
    Implementation --> UPCore
    Implementation --> UPCache
    Implementation --> UPStorage
    Implementation --> UPNetwork
    Implementation --> Foundation
```

## 分层架构详解

### 1. 客户端层 (Client Layer)
- **Flutter App**: Flutter 应用通过插件桥接调用
- **H5 App**: H5 应用通过 WebView 桥接调用
- **Native App**: 原生 iOS 应用直接调用

### 2. 插件接口层 (Plugin Interface Layer)
- **UpAppinfoPluginManager**: 插件主管理器，单例模式
- **UpPluginActionManager**: 动作管理器，负责动作注册和分发

### 3. 业务逻辑层 (Business Logic Layer)
各个具体的业务动作实现：
- **UpGetPhoneInfoAction**: 设备信息获取
- **UpGetAppInfoAction**: 应用信息获取
- **UpIsGrayModeAction/UpUpdateGrayModeAction**: 灰度模式管理
- **UpGetCacheFilesSizeAction/UpCleanCacheFilesAction**: 缓存管理
- **UpFunctionToggleAction**: 功能开关管理

### 4. 框架实现层 (Framework Implementation Layer)
- **UpAppInfoDelegate**: 协议定义层，定义所有业务接口
- **UpAppinfoPluginIMP**: 具体实现层，实现所有业务逻辑
- **UpAppinfoUtils**: 工具类，提供辅助功能

### 5. 系统依赖层 (System Dependencies Layer)
- **UPCore**: 核心框架，提供上下文和配置
- **UPCache**: 缓存管理框架
- **UPStorage**: 存储框架
- **upnetwork**: 网络框架
- **iOS Foundation**: 系统基础框架

## 设计模式

### 1. 单例模式 (Singleton Pattern)
```objective-c
+ (UpAppinfoPluginManager *)sharedInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = UpAppinfoPluginManager.new;
    });
    return manager;
}
```

### 2. 策略模式 (Strategy Pattern)
通过 `UpAppInfoDelegate` 协议实现策略模式，允许不同的实现策略。

### 3. 工厂模式 (Factory Pattern)
`UpPluginActionManager` 作为动作工厂，根据动作名称创建对应的动作实例。

### 4. 依赖注入 (Dependency Injection)
```objective-c
@property (nonatomic, strong) id<UpAppInfoDelegate> appinfoMethodDelegate;
```

## 数据流向

### 请求流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Manager as PluginManager
    participant Action as Action
    participant Delegate as Delegate
    participant System as 系统层
    
    Client->>Manager: 调用插件方法
    Manager->>Action: 分发到具体Action
    Action->>Delegate: 调用业务接口
    Delegate->>System: 访问系统资源
    System-->>Delegate: 返回数据
    Delegate-->>Action: 返回处理结果
    Action-->>Manager: 返回响应
    Manager-->>Client: 返回最终结果
```

## 模块间通信

### 1. 同步通信
- 设备信息获取
- 应用信息获取
- 灰度状态查询

### 2. 异步通信
- 缓存大小计算
- 缓存清理操作

### 3. 回调机制
```objective-c
typedef void (^CleanFinishCallback)(void);
- (void)getCacheFilesSize:(void (^)(unsigned long long fileSize))callback;
```

## 扩展性设计

### 1. 新增业务动作
1. 继承 `UpPluginAction`
2. 实现 `execute` 方法
3. 在 `UpAppinfoPluginManager` 中注册

### 2. 新增业务接口
1. 在 `UpAppInfoDelegate` 中定义协议
2. 在 `UpAppinfoPluginIMP` 中实现
3. 在对应 Action 中调用

## 错误处理机制

### 1. 统一错误码
- `000000`: 执行成功
- `900003`: 参数错误

### 2. 错误回调
```objective-c
[callback onFailure:@"900003" errMessage:errorInfo details:[result toJsonObject]];
```

## 性能优化

### 1. 懒加载
```objective-c
- (id<UpAppInfoDelegate>)appinfoMethodDelegate {
    return _appinfoMethodDelegate ? _appinfoMethodDelegate : UpAppinfoPluginIMP.new;
}
```

### 2. 异步处理
```objective-c
dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
    // 耗时操作
});
```

### 3. 缓存机制
利用 UPCache 框架进行数据缓存，减少重复计算。

## 安全性考虑

### 1. 数据隐私
- IDFA 获取需要用户授权
- 敏感信息加密存储

### 2. 参数验证
```objective-c
if (nil == params || 0 == params.allKeys.count) {
    // 参数错误处理
}
```

## 总结

UpAppinfoPlugin 采用清晰的分层架构，通过协议抽象、依赖注入等设计模式，实现了高内聚、低耦合的设计目标。架构具有良好的扩展性和维护性，能够满足不同客户端的需求。
