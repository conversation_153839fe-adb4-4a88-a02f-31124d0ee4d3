# UpAppinfoPlugin 代码库文档

## 项目概述

UpAppinfoPlugin 是海尔优家智能科技开发的 iOS 插件，为 Flutter 和 H5 应用提供应用信息获取功能。该插件基于 U+ 插件架构，提供设备信息、应用信息、缓存管理、灰度模式等核心功能。

## 项目结构

```
UpAppinfoPlugin/
├── UpAppinfoPlugin/           # 主插件模块
│   ├── Action/               # 业务动作实现
│   ├── Plugin/               # 插件管理器
│   └── doc/                  # 原有文档
├── UpAppinfoPluginFramework/  # 框架实现层
├── UpAppinfoPluginTests/      # 单元测试
├── UpAppinfoPluginDebuger/    # 调试工具
├── features/                  # BDD测试用例
├── Podfile                   # 依赖管理
└── src/docs/                 # 项目文档（本目录）
```

## 核心功能

### 1. 设备信息获取
- 获取设备型号、DPI、屏幕尺寸
- 获取设备安全区域信息
- 支持品牌信息识别

### 2. 应用信息管理
- 获取应用基本信息
- 环境配置管理
- 版本信息获取

### 3. 缓存管理
- 缓存大小计算
- 缓存清理功能
- 异步操作支持

### 4. 灰度模式
- 灰度状态查询
- 灰度模式切换
- 配置持久化

### 5. 功能开关
- 动态功能开关查询
- 配置化功能控制

## 技术架构

### 插件架构
- 基于 UpPluginFoundation 框架
- 采用 Action-Manager 设计模式
- 支持依赖注入和协议抽象

### 测试架构
- 使用 Cucumberish 进行 BDD 测试
- OCMock 提供 Mock 支持
- 完整的单元测试覆盖

## 文档导航

### 核心模块文档
- [插件管理器](./UpAppinfoPluginManager.md) - 插件核心管理组件
- [业务动作模块](./Actions.md) - 所有业务动作实现
- [框架实现层](./UpAppinfoPluginFramework.md) - 底层框架实现
- [测试模块](./Tests.md) - 测试架构和用例

### 架构文档
- [整体架构](./Architecture.md) - 系统整体架构设计
- [依赖管理](./Dependencies.md) - Podfile 和依赖分析

### 功能文档
- [BDD测试用例](./Features.md) - Cucumber 功能测试用例

## 快速开始

### 环境要求
- iOS 10.0+
- Xcode 12.0+
- CocoaPods 1.10+

### 安装步骤
1. 克隆代码库
2. 执行 `pod install`
3. 打开 `UpAppinfoPlugin.xcworkspace`
4. 运行测试：`Command + U`

### 基本使用
```objective-c
// 获取设备信息
[[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceIdentifier];

// 获取应用信息
[[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getAppInfo];
```

## 版本信息
- 当前版本：见 UpAppinfoPlugin.podspec
- 最低支持 iOS 版本：10.0
- 依赖框架：UpPluginFoundation, UPCore, UPCache 等

## 贡献指南
1. 遵循现有代码风格
2. 添加相应的单元测试
3. 更新相关文档
4. 提交前运行完整测试套件

## 联系方式
- 开发团队：海尔优家智能科技（北京）有限公司
- 代码仓库：https://gitlab.haier.net/uplus/ios/plugins/upappinfoplugin
