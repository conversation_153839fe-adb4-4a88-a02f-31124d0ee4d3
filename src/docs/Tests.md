# Tests 测试模块文档

## 概述

UpAppinfoPlugin 采用完整的测试架构，包括单元测试和 BDD（行为驱动开发）测试。测试模块确保代码质量和功能正确性，支持持续集成和回归测试。

## 测试架构

### 测试框架
- **Cucumberish**: BDD 测试框架，支持 Gherkin 语法
- **OCMock**: Objective-C Mock 框架
- **OCHamcrest**: 匹配器框架，提供丰富的断言

### 测试结构
```
UpAppinfoPluginTests/
├── CucumberRunner.m          # Cucumber 测试运行器
├── Steps/                    # 测试步骤定义
│   ├── AppinfoStep.h
│   └── AppinfoStep.m
└── Utils/                    # 测试工具类
    ├── StepsUtils.h
    ├── StepsUtils.m
    ├── UPUnitTestCallBackIMP.h
    └── UPUnitTestCallBackIMP.m
```

## CucumberRunner 测试运行器

### 基本配置
```objective-c
#import <Cucumberish/Cucumberish.h>
#import "AppinfoStep.h"

@implementation CucumberRunner

+ (void)load {
    [Cucumberish instance].fixMissingLastScenario = YES;
    
    // 注册测试步骤
    [[AppinfoStep alloc] init];
    
    // 配置测试
    [[Cucumberish instance] executeFeatures];
}

@end
```

### 测试执行流程
```mermaid
sequenceDiagram
    participant XCTest as XCTest框架
    participant Runner as CucumberRunner
    participant Cucumber as Cucumberish
    participant Steps as AppinfoStep
    participant Feature as Feature文件
    
    XCTest->>Runner: 启动测试
    Runner->>Cucumber: 初始化框架
    Runner->>Steps: 注册测试步骤
    Cucumber->>Feature: 解析feature文件
    Feature->>Steps: 执行测试步骤
    Steps-->>Cucumber: 返回测试结果
    Cucumber-->>XCTest: 报告测试结果
```

## AppinfoStep 测试步骤

### 类结构
```objective-c
@interface AppinfoStep : NSObject
@property (nonatomic, strong) UpPluginAction *action;
@property (nonatomic, strong) id executeResult;
@end
```

### 步骤定义方法

#### 1. 初始化步骤
```objective-c
Given(@"^UpAppInfoPluginManager已经初始化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
    [UpAppinfoPluginManager load];
    id imp = OCMClassMock([UpAppinfoPluginIMP class]);
    [UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate = imp;
});
```

#### 2. Action 创建步骤
```objective-c
Given(@"^创建基于\"(.*)\"平台的\"(.*)\"action$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
    NSString *actionName = args[1];
    CallMethodPlatformType platformType = platformTypeWithString(args[0]);
    self.action = ({
        Class cls = [UpPluginActionManager.sharedInstance getActionCreatorWithName:actionName];
        UpPluginAction *action = [cls new];
        action.callerType = platformType;
        action;
    });
});
```

#### 3. Mock 数据设置
```objective-c
Given(@"^执行获取设备信息成功返回数据model\"(.*)\",dpi\"(.*)\",size\"(.*)\",safeArea\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
    [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceIdentifier]).ignoringNonObjectArgs andReturn:args[0]];
    
    NSDictionary *dic = jsonObjectFromEscapedString(args[1]);
    [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceDpi]).ignoringNonObjectArgs andReturn:dic];
    
    NSDictionary *sizeDic = jsonObjectFromEscapedString(args[2]);
    [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getPhoneScreenSize]).ignoringNonObjectArgs andReturn:sizeDic];
    
    NSDictionary *safeAreaDic = jsonObjectFromEscapedString(args[3]);
    [OCMStub([[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getPhoneSafeArea]).ignoringNonObjectArgs andReturn:safeAreaDic];
});
```

#### 4. 执行步骤
```objective-c
When(@"^调用名称为\"(.*)\"的action,入参为\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
    __weak typeof(self) weakSelf = self;
    
    NSDictionary *dic = @{};
    if ([args[1] isEqualToString:@"null"] || [args[1] isEqualToString:@"{}"]) {
        dic = [self coverInvalidJsonStrToDic:args[1]];
    } else {
        dic = jsonObjectFromEscapedString(args[1]);
    }
    
    [self.action execute:args[0]
                  params:dic
                 options:nil
             finishBlock:[[UPUnitTestCallBackIMP alloc] initWithCallback:^(id _Nonnull retData) {
                 weakSelf.executeResult = retData;
             }]];
});
```

#### 5. 验证步骤
```objective-c
Then(@"^执行名称为\"(.*)\"的action成功,成功码:\"(.*)\",成功信息:\"(.*)\",返回数据:\"(.*)\"$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
    CCIAssert([self.executeResult[@"retCode"] isEqualToString:args[1]], @"执行%@失败,retCode:%@与预期不符", args[0], self.executeResult[@"retCode"]);
    CCIAssert([self.executeResult[@"retInfo"] isEqualToString:args[2]], @"执行%@失败,retInfo:%@与预期不符", args[0], self.executeResult[@"retInfo"]);
    
    if ([self.executeResult[@"retData"] isKindOfClass:[NSDictionary class]]) {
        NSDictionary *expectRetData = jsonObjectFromEscapedString1(args[3]);
        CCIAssert(isDictionaryEqual(expectRetData, self.executeResult[@"retData"]), @"执行%@失败,retData%@与预期不符", args[3], expectRetData);
    }
});
```

## 测试工具类

### StepsUtils 工具类

#### JSON 解析工具
```objective-c
// JSON 字符串转对象
NSDictionary *jsonObjectFromEscapedString(NSString *escapedString);

// 字典比较工具
BOOL isDictionaryEqual(NSDictionary *dict1, NSDictionary *dict2);

// 平台类型转换
CallMethodPlatformType platformTypeWithString(NSString *platformString);

// 字符串排序
NSString *sortedString(NSString *jsonString);

// 字典转JSON字符串
NSString *dictionaryToJson(NSDictionary *dictionary);
```

### UPUnitTestCallBackIMP 回调实现

#### 回调接口
```objective-c
@interface UPUnitTestCallBackIMP : NSObject <UPPCallBackProtocol>

- (instancetype)initWithCallback:(void (^)(id retData))callback;

@end
```

#### 实现逻辑
```objective-c
@implementation UPUnitTestCallBackIMP {
    void (^_callback)(id retData);
}

- (instancetype)initWithCallback:(void (^)(id))callback {
    self = [super init];
    if (self) {
        _callback = callback;
    }
    return self;
}

- (void)onSuccess:(id)retData {
    if (_callback) {
        _callback(retData);
    }
}

- (void)onFailure:(NSString *)retCode errMessage:(NSString *)errMessage details:(id)details {
    if (_callback) {
        _callback(details);
    }
}

@end
```

## Mock 策略

### 1. 依赖注入 Mock
```objective-c
// 注入 Mock 对象
id mockDelegate = OCMClassMock([UpAppinfoPluginIMP class]);
[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate = mockDelegate;
```

### 2. 方法 Stub
```objective-c
// 设置方法返回值
[OCMStub([mockDelegate getDeviceIdentifier]) andReturn:@"iPhone13,2"];

// 设置异步回调
[OCMStub([mockDelegate getCacheFilesSize:[OCMArg any]]) andDo:^(NSInvocation *invocation) {
    void (^callback)(unsigned long long);
    [invocation getArgument:&callback atIndex:2];
    callback(1024000);
}];
```

### 3. 验证调用
```objective-c
// 验证方法被调用
OCMVerify([mockDelegate getDeviceIdentifier]);

// 验证方法调用次数
OCMVerify(times(1), [mockDelegate isGrayMode]);
```

## 测试数据管理

### 参数化测试
使用 Scenario Outline 进行参数化测试：
```gherkin
Scenario Outline: [10000]预期获取设备信息成功
  Given 创建基于"<platform>"平台的"getPhoneInfoForAppInfo"action
  Given 执行获取设备信息成功返回数据model"<deviceIdentifier>",dpi"<deviceDpi>",size"<screenSize>",safeArea"<safeArea>"
  When 调用名称为"getPhoneInfoForAppInfo"的action,入参为"null"
  Then 执行名称为"getPhoneInfoForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"<retData>"

Examples:
  | platform | deviceIdentifier | deviceDpi | screenSize | safeArea | retData |
  | Flutter  | iPhone13,2       | {...}     | {...}      | {...}    | {...}   |
```

### 测试数据表格
支持使用数据表格进行复杂数据测试：
```gherkin
Then 执行名称为"getAppInfoForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:
  | appId    | MB-UZHSH-0001 |
  | platform | iOS           |
  | env      | test          |
```

## 测试覆盖率

### 功能覆盖
- ✅ 设备信息获取
- ✅ 应用信息获取  
- ✅ 灰度模式管理
- ✅ 缓存管理
- ✅ 功能开关查询

### 场景覆盖
- ✅ 正常流程测试
- ✅ 异常参数测试
- ✅ 边界条件测试
- ✅ 多平台兼容测试

## 持续集成

### 自动化测试
```bash
# 运行所有测试
xcodebuild test -workspace UpAppinfoPlugin.xcworkspace -scheme UpAppinfoPlugin -destination 'platform=iOS Simulator,name=iPhone 13'

# 运行特定测试
xcodebuild test -workspace UpAppinfoPlugin.xcworkspace -scheme UpAppinfoPlugin -only-testing:UpAppinfoPluginTests
```

### 测试报告
- 使用 XCTest 生成测试报告
- 集成 Cucumberish 生成 BDD 报告
- 支持 CI/CD 流水线集成

## 最佳实践

### 1. 测试编写原则
- 每个功能都要有对应的测试用例
- 测试用例要覆盖正常和异常场景
- 使用描述性的测试名称
- 保持测试的独立性

### 2. Mock 使用建议
- 只 Mock 外部依赖
- 避免过度 Mock
- 使用真实数据进行集成测试
- 及时清理 Mock 状态

### 3. 维护建议
- 定期更新测试用例
- 保持测试代码的简洁性
- 及时修复失败的测试
- 添加新功能时同步添加测试

## 故障排除

### 常见问题
1. **测试失败**: 检查 Mock 设置是否正确
2. **数据不匹配**: 验证测试数据格式
3. **异步测试问题**: 确保正确处理异步回调
4. **平台兼容性**: 检查不同平台的测试配置

### 调试技巧
- 使用断点调试测试步骤
- 检查 Mock 对象的调用记录
- 验证测试数据的正确性
- 查看详细的错误日志
