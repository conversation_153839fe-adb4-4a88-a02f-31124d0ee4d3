# UpAppinfoPluginFramework 框架实现层文档

## 概述

UpAppinfoPluginFramework 是插件的核心实现层，提供了所有业务逻辑的具体实现。该框架采用协议-实现分离的设计模式，通过 `UpAppInfoDelegate` 协议定义接口，`UpAppinfoPluginIMP` 类提供具体实现。

## 框架结构

### 核心组件
- **UpAppInfoDelegate.h**: 协议定义文件
- **UpAppinfoPluginIMP.h/m**: 具体实现文件
- **UpAppinfoUtils.h/m**: 工具类文件
- **UpAppinfoPluginFramework.h**: 框架头文件

## UpAppInfoDelegate 协议

### 协议定义
```objective-c
@protocol UpAppInfoDelegate <NSObject>

/// 获取设备型号标识
- (NSString *)getDeviceIdentifier;

/// 获取App信息
- (NSDictionary *)getAppInfo;

/// 获取toggle开关信息
- (id)getToggleInfo:(NSString *)key;

/// 获取设备宽高像素值
- (NSDictionary *)getDeviceDpi;

/// 获取手机屏幕尺寸
- (NSDictionary *)getPhoneScreenSize;

/// 获取手机安全区域
- (NSDictionary *)getPhoneSafeArea;

/// 获取灰度状态
- (BOOL)isGrayMode;

/// 更新灰度状态
- (void)setGrayMode:(BOOL)grayMode;

/// 获取缓存文件大小
- (void)getCacheFilesSize:(void (^)(unsigned long long fileSize))callback;

/// 清理缓存文件
- (void)cleanCacheFiles:(CleanFinishCallback)callBack;

@end
```

### 回调类型定义
```objective-c
typedef void (^CleanFinishCallback)(void);
```

## UpAppinfoPluginIMP 实现类

### 类结构
```objective-c
@interface UpAppinfoPluginIMP : NSObject <UpAppInfoDelegate>
@end

@implementation UpAppinfoPluginIMP {
    CleanFinishCallback _callback;
    BOOL _isSyncCleanFinish;  // 标记同步清除缓存是否完成
    BOOL _isAsyncCleanFinish; // 标记异步清除缓存是否完成
}
```

### 依赖框架
```objective-c
#import <UPCore/UPContext.h>
#import <sys/utsname.h>
#import "UpAppinfoUtils.h"
#import <upnetwork/UPNetworkSettings.h>
#import <UPCache/CHaierCleanCacheTool.h>
#import <UPStorage/UPStorage.h>
#import <UPCore/UPFunctionToggle.h>
```

## 核心功能实现

### 1. 设备信息获取

#### 设备标识获取
```objective-c
- (NSString *)getDeviceIdentifier {
    struct utsname systemInfo;
    uname(&systemInfo);
    return [NSString stringWithCString:systemInfo.machine encoding:NSUTF8StringEncoding];
}
```

#### 设备DPI获取
```objective-c
- (NSDictionary *)getDeviceDpi {
    CGRect screenBounds = [[UIScreen mainScreen] bounds];
    CGFloat screenScale = [[UIScreen mainScreen] scale];
    CGSize screenSize = CGSizeMake(screenBounds.size.width * screenScale, 
                                   screenBounds.size.height * screenScale);
    return @{
        @"x" : @(screenSize.width),
        @"y" : @(screenSize.height)
    };
}
```

#### 屏幕尺寸获取
```objective-c
- (NSDictionary *)getPhoneScreenSize {
    CGRect screenBounds = [[UIScreen mainScreen] bounds];
    CGFloat screenScale = [[UIScreen mainScreen] scale];
    CGSize screenSize = CGSizeMake(screenBounds.size.width * screenScale, 
                                   screenBounds.size.height * screenScale);
    return @{
        @"width" : @(screenSize.width),
        @"height" : @(screenSize.height)
    };
}
```

#### 安全区域获取
```objective-c
- (NSDictionary *)getPhoneSafeArea {
    UIEdgeInsets safeArea = UIEdgeInsetsMake(20, 0, 0, 0);
    if (@available(iOS 11.0, *)) {
        safeArea = [UIApplication sharedApplication].keyWindow.safeAreaInsets;
    }
    return @{
        @"top" : @(safeArea.top),
        @"bottom" : @(safeArea.bottom)
    };
}
```

### 2. 应用信息获取

#### 应用信息组装
```objective-c
- (NSDictionary *)getAppInfo {
    NSString *appId = [UPNetworkSettings sharedSettings].appID;
    NSString *appKey = [UPNetworkSettings sharedSettings].appKey;
    NSString *haierUserCenterUrl = UPContext.sharedInstance.haierUserCenterUrl ?: @"";
    NSString *haierClientId = UPContext.sharedInstance.haierClientId ?: @"";
    NSString *haierClientSecret = UPContext.sharedInstance.haierClientSecret ?: @"";
    
    NSString *appVersion = [UPContext sharedInstance].appVersion ?: @"";
    NSString *buildVersion = [UPStorage getStringValue:@"kAppBuildVersion" defaultValue:@"0"];
    NSString *clientId = [UPNetworkSettings sharedSettings].clientID;
    
    // 更多字段组装...
    
    NSDictionary *dic = @{
        @"appId" : appId,
        @"env" : env,
        @"idfa" : idfaStr,
        @"appKey" : appKey,
        @"testMode" : testMode ? testMode : [NSNumber numberWithBool:false],
        @"haierUserCenterUrl" : haierUserCenterUrl,
        @"haierClientId" : haierClientId,
        @"platform" : @"iOS",
        @"grayMode" : grayMode ? grayMode : [NSNumber numberWithBool:false],
        @"OSversion" : systemVersion,
        @"appVersion" : appVersion,
        @"versionCode" : @([buildVersion integerValue]),
        @"clientId" : clientId,
        @"haierClientSecret" : haierClientSecret,
        @"isPreViewEnv" : @(preViewEnv)
    };
    return dic;
}
```

### 3. 灰度模式管理

#### 常量定义
```objective-c
NSString *const kAppConfigGrayModeKey = @"kUPDeviceTestState";
```

#### 灰度状态查询
```objective-c
- (BOOL)isGrayMode {
    NSNumber *grayMode = [[NSUserDefaults standardUserDefaults] objectForKey:kAppConfigGrayModeKey];
    return grayMode ? grayMode.boolValue : NO;
}
```

#### 灰度状态设置
```objective-c
- (void)setGrayMode:(BOOL)grayMode {
    [[NSUserDefaults standardUserDefaults] setObject:@(grayMode) forKey:kAppConfigGrayModeKey];
    [[NSUserDefaults standardUserDefaults] synchronize];
}
```

### 4. 缓存管理

#### 缓存大小计算
```objective-c
- (void)getCacheFilesSize:(void (^)(unsigned long long))callback {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        unsigned long long totalSize = 0;
        
        // 计算各种缓存大小
        // 1. 系统缓存
        // 2. 应用缓存
        // 3. 第三方库缓存
        
        dispatch_async(dispatch_get_main_queue(), ^{
            callback(totalSize);
        });
    });
}
```

#### 缓存清理实现
```objective-c
- (void)cleanCacheFiles:(CleanFinishCallback)callBack {
    _callback = callBack;
    _isSyncCleanFinish = NO;
    _isAsyncCleanFinish = NO;
    
    // 同步清理
    [self syncCleanCache];
    
    // 异步清理
    [self asyncCleanCache];
}

- (void)syncCleanCache {
    // 清理系统缓存
    // 清理临时文件
    _isSyncCleanFinish = YES;
    [self checkCleanFinish];
}

- (void)asyncCleanCache {
    // 清理第三方库缓存
    // 使用 UPResource 清理
    _isAsyncCleanFinish = YES;
    [self checkCleanFinish];
}

- (void)checkCleanFinish {
    if (_isSyncCleanFinish && _isAsyncCleanFinish) {
        if (_callback) {
            _callback();
        }
    }
}
```

### 5. 功能开关查询

#### Toggle 信息获取
```objective-c
- (id)getToggleInfo:(NSString *)key {
    return [UPFunctionToggle.shareInstance toggleValueForKey:key];
}
```

## 工具类 UpAppinfoUtils

### 系统版本获取
```objective-c
NSString *getSystemVersion(void) {
    return [[UIDevice currentDevice] systemVersion];
}
```

### 环境判断
```objective-c
BOOL isPreviewEnvironment(void) {
    // 判断是否为预览环境
    return [UPContext sharedInstance].isPreviewEnv;
}
```

## 缓存清理回调协议

### UPResCacheCleanCallback 协议
```objective-c
@interface UpAppinfoPluginIMP () <UPResCacheCleanCallback>
@end

- (void)onCleanFinish {
    _isAsyncCleanFinish = YES;
    [self checkCleanFinish];
}
```

## 线程安全

### 异步操作处理
```objective-c
dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
    // 后台线程执行耗时操作
    dispatch_async(dispatch_get_main_queue(), ^{
        // 主线程回调
    });
});
```

### 数据同步
```objective-c
[[NSUserDefaults standardUserDefaults] synchronize];
```

## 错误处理

### 空值保护
```objective-c
NSString *appVersion = [UPContext sharedInstance].appVersion ?: @"";
```

### 版本兼容
```objective-c
if (@available(iOS 11.0, *)) {
    safeArea = [UIApplication sharedApplication].keyWindow.safeAreaInsets;
} else {
    safeArea = UIEdgeInsetsMake(20, 0, 0, 0);
}
```

## 性能优化

### 1. 懒加载
- 按需创建对象
- 避免不必要的计算

### 2. 缓存机制
- 利用系统缓存
- 避免重复计算

### 3. 异步处理
- 耗时操作后台执行
- 主线程回调更新UI

## 扩展指南

### 添加新接口
1. 在 `UpAppInfoDelegate` 协议中添加方法声明
2. 在 `UpAppinfoPluginIMP` 中实现具体逻辑
3. 在对应的 Action 中调用新接口

### 最佳实践
- 保持接口简洁明确
- 使用异步回调处理耗时操作
- 添加适当的错误处理
- 考虑线程安全问题
- 提供版本兼容性支持
