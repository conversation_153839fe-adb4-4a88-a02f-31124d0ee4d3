# Actions 业务动作模块文档

## 概述

Actions 模块包含了 UpAppinfoPlugin 的所有业务动作实现，每个动作负责处理特定的业务逻辑。所有动作都继承自 `UpPluginAction` 基类，遵循统一的执行模式。

## 动作架构

### 基类结构
```objective-c
@interface UpPluginAction : NSObject <UpPluginActionProtocol>
- (void)execute:(NSString *)action 
         params:(NSDictionary *)params 
        options:(id)options 
    finishBlock:(id<UPPCallBackProtocol>)callback;
@end
```

### 执行流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Manager as ActionManager
    participant Action as 具体Action
    participant Delegate as Delegate
    participant Callback as 回调
    
    Client->>Manager: 调用动作
    Manager->>Action: execute方法
    Action->>Delegate: 调用业务方法
    Delegate-->>Action: 返回结果
    Action->>Callback: onSuccess/onFailure
    Callback-->>Client: 返回最终结果
```

## 动作列表

### 1. UpGetPhoneInfoAction - 设备信息获取

#### 功能描述
获取设备的详细信息，包括型号、DPI、屏幕尺寸、安全区域和品牌信息。

#### 动作名称
```objective-c
NSString *const GetDeviceModel_ActionName = @"getPhoneInfoForAppInfo";
```

#### 实现逻辑
```objective-c
- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback {
    NSString *deviceIdentifier = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceIdentifier];
    NSString *deviceModel = [self currentDeviceModel:deviceIdentifier];
    NSDictionary *deviceDpi = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getDeviceDpi];
    NSDictionary *screenSize = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getPhoneScreenSize];
    NSDictionary *safeArea = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getPhoneSafeArea];
    
    NSDictionary *dic = @{ 
        @"model" : deviceModel,
        @"dpi" : deviceDpi,
        @"brand" : @"apple",
        @"size" : screenSize,
        @"safeArea" : safeArea 
    };
    
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功" withData:dic];
    [callback onSuccess:[result toJsonObject]];
}
```

#### 返回数据结构
```json
{
    "model": "iPhone 13 Pro",
    "dpi": {"x": 1170, "y": 2532},
    "brand": "apple",
    "size": {"width": 390, "height": 844},
    "safeArea": {"top": 47, "bottom": 34}
}
```

### 2. UpGetAppInfoAction - 应用信息获取

#### 功能描述
获取应用的基本信息，包括应用ID、版本、环境配置等。

#### 动作名称
```objective-c
NSString *const GetAppInfo_ActionName = @"getAppInfoForAppInfo";
```

#### 实现逻辑
```objective-c
- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback {
    NSDictionary *appInfo = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getAppInfo];
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功" withData:appInfo];
    [callback onSuccess:[result toJsonObject]];
}
```

### 3. UpIsGrayModeAction - 灰度模式查询

#### 功能描述
查询当前应用的灰度模式状态。

#### 动作名称
```objective-c
NSString *const IsGrayMode_ActionName = @"isGrayModeForAppInfo";
```

#### 实现逻辑
```objective-c
- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback {
    BOOL grayMode = [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate isGrayMode];
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功" withData:@(grayMode)];
    [callback onSuccess:[result toJsonObject]];
}
```

### 4. UpUpdateGrayModeAction - 灰度模式设置

#### 功能描述
设置应用的灰度模式状态。

#### 动作名称
```objective-c
NSString *const UpdateGrayMode_ActionName = @"setGrayModeForAppInfo";
```

#### 参数验证
```objective-c
if (nil == params || 0 == params.allKeys.count) {
    NSString *errorInfo = @"参数错误";
    UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:@"900003" retInfo:errorInfo];
    [callback onFailure:@"900003" errMessage:errorInfo details:[result toJsonObject]];
    return;
}
```

#### 参数格式
```json
{
    "grayMode": true
}
```

### 5. UpGetCacheFilesSizeAction - 缓存大小获取

#### 功能描述
异步获取应用缓存文件的总大小。

#### 动作名称
```objective-c
NSString *const GetCacheFileSize_ActionName = @"getCacheSizeForAppInfo";
```

#### 异步实现
```objective-c
- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback {
    [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate getCacheFilesSize:^(unsigned long long fileSize) {
        UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功" withData:@(fileSize)];
        [callback onSuccess:[result toJsonObject]];
    }];
}
```

### 6. UpCleanCacheFilesAction - 缓存清理

#### 功能描述
异步清理应用缓存文件。

#### 动作名称
```objective-c
NSString *const CleanCacheFiles_Action = @"cleanCacheForAppInfo";
```

#### 后台执行
```objective-c
- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback {
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        [[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate cleanCacheFiles:^{
            dispatch_async(dispatch_get_main_queue(), ^{
                UPCommonResult *result = [[UPCommonResult alloc] initWithRetCode:@"000000" retInfo:@"执行成功"];
                [callback onSuccess:[result toJsonObject]];
            });
        }];
    });
}
```

### 7. UpFunctionToggleAction - 功能开关查询 (Swift)

#### 功能描述
查询指定功能开关的状态。

#### 动作名称
```swift
let FunctionToggleActionName = "FunctionToggleActionForAppInfo"
```

#### 实现逻辑
```swift
@objc public override func execute(_ action: String, params: [AnyHashable : Any], options: Any?, finishBlock callback: UPPCallBackProtocol?) {
    if let key = params["key"] as? String, !key.isEmpty {
        let retData = UpAppinfoPluginManager.sharedInstance().appinfoMethodDelegate.getToggleInfo(key)
        let result = UPCommonResult(successRetData: retData)
        callback?.onSuccess(result.toJsonObject())
    } else {
        let result = UPCommonResult.parameterError()
        callback?.onFailure(result.retCode, errMessage: result.retCode, details:result.toJsonObject());
    }
}
```

## 错误处理

### 统一错误码
- `000000`: 执行成功
- `900003`: 参数错误

### 错误响应格式
```json
{
    "retCode": "900003",
    "retInfo": "参数错误",
    "retData": null
}
```

## 性能优化

### 1. 异步处理
对于耗时操作（如缓存计算和清理），使用后台队列处理：
```objective-c
dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
    // 耗时操作
    dispatch_async(dispatch_get_main_queue(), ^{
        // 回调主线程
    });
});
```

### 2. 懒加载
设备型号映射采用懒加载方式：
```objective-c
- (NSString *)currentDeviceModel:(NSString *)deviceIdentifier {
    NSBundle *bundle = [NSBundle bundleForClass:[self class]];
    NSString *filePath = [bundle pathForResource:@"DeviceModels" ofType:@"plist"];
    NSDictionary *dics = [NSDictionary dictionaryWithContentsOfFile:filePath];
    // ...
}
```

## 扩展指南

### 添加新动作
1. **创建动作类**
```objective-c
@interface UpNewAction : UpPluginAction
@end

@implementation UpNewAction
+ (NSString *)action {
    return @"newActionName";
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback {
    // 实现业务逻辑
    // 调用委托方法
    // 返回结果
}
@end
```

2. **注册动作**
在 `UpAppinfoPluginManager.m` 中添加到动作列表。

3. **添加委托方法**
如需新的业务逻辑，在 `UpAppInfoDelegate` 协议中添加方法定义。

### 最佳实践
- 保持动作类的单一职责
- 使用统一的错误处理机制
- 对耗时操作使用异步处理
- 添加适当的参数验证
- 提供清晰的返回数据结构
