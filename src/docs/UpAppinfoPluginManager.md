# UpAppinfoPluginManager 插件管理器文档

## 概述

`UpAppinfoPluginManager` 是 UpAppinfoPlugin 的核心管理组件，负责插件的初始化、动作注册和业务委托管理。采用单例模式设计，确保全局唯一性。

## 类结构

### 头文件 (UpAppinfoPluginManager.h)
```objective-c
@interface UpAppinfoPluginManager : NSObject

@property (nonatomic, strong) id<UpAppInfoDelegate> appinfoMethodDelegate;
+ (UpAppinfoPluginManager *)sharedInstance;

@end
```

### 实现文件 (UpAppinfoPluginManager.m)
- 单例实现
- 动作注册
- 委托管理

## 核心功能

### 1. 单例管理

#### 实现方式
```objective-c
+ (UpAppinfoPluginManager *)sharedInstance {
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        manager = UpAppinfoPluginManager.new;
    });
    return manager;
}
```

#### 特点
- 线程安全：使用 `dispatch_once` 确保线程安全
- 全局唯一：整个应用生命周期内只有一个实例
- 懒加载：首次调用时才创建实例

### 2. 动作注册

#### 注册机制
```objective-c
+ (void)load {
    NSArray<Class<UpPluginActionProtocol>> *actions = @[
        UpGetPhoneInfoAction.class,
        UpGetAppInfoAction.class,
        UpIsGrayModeAction.class,
        UpUpdateGrayModeAction.class,
        UpGetCacheFilesSizeAction.class,
        UpCleanCacheFilesAction.class,
        UpFunctionToggleAction.class
    ];
    UpPluginActionManager *manager = [UpPluginActionManager sharedInstance];
    [actions enumerateObjectsUsingBlock:^(Class<UpPluginActionProtocol> _Nonnull obj, NSUInteger idx, BOOL *_Nonnull stop) {
        [manager appendAction:[obj action] creator:obj];
    }];
}
```

#### 注册的动作列表
| 动作类 | 动作名称 | 功能描述 |
|--------|----------|----------|
| UpGetPhoneInfoAction | getPhoneInfoForAppInfo | 获取设备信息 |
| UpGetAppInfoAction | getAppInfoForAppInfo | 获取应用信息 |
| UpIsGrayModeAction | isGrayModeForAppInfo | 查询灰度模式状态 |
| UpUpdateGrayModeAction | setGrayModeForAppInfo | 设置灰度模式 |
| UpGetCacheFilesSizeAction | getCacheSizeForAppInfo | 获取缓存大小 |
| UpCleanCacheFilesAction | cleanCacheForAppInfo | 清理缓存 |
| UpFunctionToggleAction | FunctionToggleActionForAppInfo | 功能开关查询 |

### 3. 委托管理

#### 委托属性
```objective-c
@property (nonatomic, strong) id<UpAppInfoDelegate> appinfoMethodDelegate;
```

#### 懒加载实现
```objective-c
- (id<UpAppInfoDelegate>)appinfoMethodDelegate {
    return _appinfoMethodDelegate ? _appinfoMethodDelegate : UpAppinfoPluginIMP.new;
}
```

#### 委托模式优势
- **解耦合**: 管理器不直接依赖具体实现
- **可测试**: 可以注入 Mock 对象进行测试
- **可扩展**: 可以替换不同的实现策略

## 依赖关系

### 导入的框架
```objective-c
#import "UpAppinfoPluginIMP.h"
#import <UpPluginFoundation/UpPluginFoundation.h>
#import "UpAppinfoPlugin-Swift.h"
```

### 依赖的动作类
- UpGetPhoneInfoAction
- UpGetAppInfoAction
- UpIsGrayModeAction
- UpUpdateGrayModeAction
- UpGetCacheFilesSizeAction
- UpCleanCacheFilesAction
- UpFunctionToggleAction (Swift)

## 生命周期

### 初始化流程
```mermaid
sequenceDiagram
    participant App as 应用启动
    participant Load as +load方法
    participant Manager as PluginManager
    participant ActionMgr as ActionManager
    
    App->>Load: 类加载
    Load->>Manager: 获取动作列表
    Load->>ActionMgr: 注册动作
    ActionMgr->>ActionMgr: 存储动作映射
    
    Note over Load,ActionMgr: 动作注册完成
```

### 使用流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Manager as PluginManager
    participant Delegate as Delegate
    participant Impl as Implementation
    
    Client->>Manager: sharedInstance
    Manager->>Manager: 创建单例(如果不存在)
    Client->>Manager: appinfoMethodDelegate
    Manager->>Impl: 创建默认实现(如果未设置)
    Manager-->>Client: 返回委托对象
```

## 设计模式

### 1. 单例模式 (Singleton)
- **目的**: 确保全局唯一的管理器实例
- **实现**: 使用 `dispatch_once` 保证线程安全
- **优势**: 全局访问点，资源共享

### 2. 委托模式 (Delegate)
- **目的**: 将具体业务逻辑委托给专门的实现类
- **实现**: 通过 `UpAppInfoDelegate` 协议
- **优势**: 职责分离，便于测试和扩展

### 3. 工厂模式 (Factory)
- **目的**: 动态创建和管理动作实例
- **实现**: 通过 `UpPluginActionManager`
- **优势**: 统一管理，动态分发

## 扩展指南

### 添加新动作
1. **创建动作类**
```objective-c
@interface UpNewAction : UpPluginAction
@end

@implementation UpNewAction
+ (NSString *)action {
    return @"newActionForAppInfo";
}

- (void)execute:(NSString *)action params:(NSDictionary *)params options:(id)options finishBlock:(id<UPPCallBackProtocol>)callback {
    // 实现业务逻辑
}
@end
```

2. **注册动作**
```objective-c
// 在 UpAppinfoPluginManager.m 的 +load 方法中添加
NSArray<Class<UpPluginActionProtocol>> *actions = @[
    // 现有动作...
    UpNewAction.class  // 新增动作
];
```

### 自定义委托实现
```objective-c
// 创建自定义实现
@interface CustomAppInfoDelegate : NSObject <UpAppInfoDelegate>
@end

// 注入自定义实现
[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate = [[CustomAppInfoDelegate alloc] init];
```

## 测试支持

### Mock 注入
```objective-c
// 在测试中注入 Mock 对象
id mockDelegate = OCMProtocolMock(@protocol(UpAppInfoDelegate));
[UpAppinfoPluginManager sharedInstance].appinfoMethodDelegate = mockDelegate;
```

### 测试验证
```objective-c
// 验证方法调用
OCMVerify([mockDelegate getDeviceIdentifier]);
```

## 最佳实践

### 1. 使用建议
- 始终通过 `sharedInstance` 获取管理器实例
- 不要直接创建 `UpAppinfoPluginManager` 实例
- 在测试中使用 Mock 对象替换默认委托

### 2. 注意事项
- `+load` 方法在类加载时自动执行，无需手动调用
- 委托对象支持运行时替换，但要注意线程安全
- 新增动作时要确保实现 `UpPluginActionProtocol` 协议

### 3. 性能考虑
- 单例模式避免了重复创建对象的开销
- 懒加载减少了不必要的内存占用
- 动作注册在应用启动时完成，运行时无额外开销

## 故障排除

### 常见问题
1. **动作未注册**: 检查是否在 `+load` 方法中添加了动作类
2. **委托为空**: 检查是否正确设置了 `appinfoMethodDelegate`
3. **方法未找到**: 检查动作名称是否与注册时一致

### 调试技巧
- 使用断点调试 `+load` 方法确认动作注册
- 检查 `UpPluginActionManager` 中的动作映射
- 验证委托对象是否正确实现了协议方法
