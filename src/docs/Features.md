# Features BDD测试用例文档

## 概述

Features 目录包含了使用 Gherkin 语法编写的 BDD（行为驱动开发）测试用例。这些测试用例描述了系统的预期行为，支持自动化测试执行。

## 测试文件结构

```
features/
├── GetAppInfo.feature        # 应用信息获取测试
├── GetPhoneInfo.feature      # 设备信息获取测试
├── GrayMode.feature          # 灰度模式测试
├── CacheManagement.feature   # 缓存管理测试
└── FunctionToggle.feature    # 功能开关测试
```

## Gherkin 语法说明

### 基本结构
```gherkin
Feature: 功能描述
  Background: 背景步骤（可选）
  
  Scenario: 场景描述
    Given 前置条件
    When 执行动作
    Then 验证结果
    
  Scenario Outline: 参数化场景
    Given 前置条件 "<参数>"
    When 执行动作 "<参数>"
    Then 验证结果 "<参数>"
    
    Examples:
      | 参数 | 值1 | 值2 |
```

### 标签系统
- `@android_ignore`: Android 平台忽略
- `@ios_ignore`: iOS 平台忽略
- `@smoke`: 冒烟测试
- `@regression`: 回归测试

## GetPhoneInfo.feature 设备信息测试

### 功能描述
```gherkin
Feature: 获取设备信息
  作为一个移动应用开发者
  我想要获取设备的详细信息
  以便为用户提供个性化的体验
```

### 测试场景

#### 1. iOS 设备信息获取
```gherkin
@android_ignore
Scenario Outline: [10000]预期获取设备信息成功
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"<platform>"平台的"getPhoneInfoForAppInfo"action
  Given 执行获取设备信息成功返回数据model"<deviceIdentifier>",dpi"<deviceDpi>",size"<screenSize>",safeArea"<safeArea>"
  When 调用名称为"getPhoneInfoForAppInfo"的action,入参为"null"
  Then 执行名称为"getPhoneInfoForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"<retData>"
```

#### 2. 测试数据示例
```gherkin
Examples:
  | platform | deviceIdentifier | deviceDpi              | screenSize                      | safeArea                  | retData |
  | Flutter  | iPhone13,2       | {\"x\":1170,\"y\":2532} | {\"width\":390,\"height\":844} | {\"top\":47,\"bottom\":34} | {\"model\":\"iPhone 13\", \"dpi\":{\"x\":1170,\"y\":2532}, \"brand\":\"apple\", \"size\":{\"width\":390,\"height\":844}, \"safeArea\":{\"top\":47,\"bottom\":34}} |
```

### 覆盖的设备类型
- **iPhone 系列**: iPhone 3,1 到 iPhone 13,4
- **iPod 系列**: iPod1,1 到 iPod9,1  
- **iPad 系列**: iPad1,1 到 iPad13,1
- **Apple TV**: AppleTV2,1 到 AppleTV5,3
- **模拟器**: i386, x86_64

## GetAppInfo.feature 应用信息测试

### 功能描述
```gherkin
Feature: 获取应用信息
  作为一个移动应用
  我需要获取当前应用的配置信息
  以便正确初始化和运行
```

### 测试场景

#### 1. 基本应用信息获取
```gherkin
Scenario: [20000]预期获取应用信息成功
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"getAppInfoForAppInfo"action
  Given 执行获取应用信息成功返回数据
  When 调用名称为"getAppInfoForAppInfo"的action,入参为"null"
  Then 执行名称为"getAppInfoForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功"
```

#### 2. 应用信息字段验证
```gherkin
Then 返回的应用信息包含以下字段:
  | 字段名 | 类型 | 必填 |
  | appId | String | 是 |
  | platform | String | 是 |
  | env | String | 是 |
  | appVersion | String | 是 |
  | OSversion | String | 是 |
```

## GrayMode.feature 灰度模式测试

### 功能描述
```gherkin
Feature: 灰度模式管理
  作为一个应用管理员
  我需要能够查询和设置灰度模式
  以便控制功能的发布策略
```

### 测试场景

#### 1. 灰度状态查询
```gherkin
Scenario: [30000]查询灰度模式状态
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"isGrayModeForAppInfo"action
  Given 当前灰度模式状态为"false"
  When 调用名称为"isGrayModeForAppInfo"的action,入参为"null"
  Then 执行名称为"isGrayModeForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"false"
```

#### 2. 灰度模式设置
```gherkin
Scenario Outline: [30001]设置灰度模式
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"setGrayModeForAppInfo"action
  When 调用名称为"setGrayModeForAppInfo"的action,入参为"<params>"
  Then 执行名称为"setGrayModeForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功"
  
  Examples:
    | params |
    | {\"grayMode\":true} |
    | {\"grayMode\":false} |
```

#### 3. 参数错误测试
```gherkin
Scenario: [30002]设置灰度模式参数错误
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"setGrayModeForAppInfo"action
  When 调用名称为"setGrayModeForAppInfo"的action,入参为"null"
  Then 执行名称为"setGrayModeForAppInfo"的action失败,错误码:"900003",错误信息:"参数错误"
```

## CacheManagement.feature 缓存管理测试

### 功能描述
```gherkin
Feature: 缓存管理
  作为一个移动应用用户
  我需要能够查看和清理应用缓存
  以便释放存储空间和提升性能
```

### 测试场景

#### 1. 缓存大小获取
```gherkin
Scenario: [40000]获取缓存文件大小
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"getCacheSizeForAppInfo"action
  Given 当前缓存大小为"1024000"字节
  When 调用名称为"getCacheSizeForAppInfo"的action,入参为"null"
  Then 执行名称为"getCacheSizeForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"1024000"
```

#### 2. 缓存清理
```gherkin
Scenario: [40001]清理缓存文件
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"cleanCacheForAppInfo"action
  When 调用名称为"cleanCacheForAppInfo"的action,入参为"null"
  Then 执行名称为"cleanCacheForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功"
```

#### 3. 异步操作测试
```gherkin
Scenario: [40002]异步缓存操作
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"getCacheSizeForAppInfo"action
  When 异步调用名称为"getCacheSizeForAppInfo"的action
  Then 在5秒内收到回调结果
  And 回调结果包含缓存大小信息
```

## FunctionToggle.feature 功能开关测试

### 功能描述
```gherkin
Feature: 功能开关查询
  作为一个应用开发者
  我需要能够查询功能开关的状态
  以便动态控制功能的可用性
```

### 测试场景

#### 1. 功能开关查询
```gherkin
Scenario Outline: [50000]查询功能开关状态
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"FunctionToggleActionForAppInfo"action
  Given 功能开关"<key>"的值为"<value>"
  When 调用名称为"FunctionToggleActionForAppInfo"的action,入参为"{\"key\":\"<key>\"}"
  Then 执行名称为"FunctionToggleActionForAppInfo"的action成功,成功码:"000000",成功信息:"执行成功",返回数据:"<value>"
  
  Examples:
    | key | value |
    | feature_a | true |
    | feature_b | false |
    | feature_c | "config_value" |
```

#### 2. 无效参数测试
```gherkin
Scenario: [50001]功能开关查询参数错误
  Given UpAppInfoPluginManager已经初始化
  Given 创建基于"Flutter"平台的"FunctionToggleActionForAppInfo"action
  When 调用名称为"FunctionToggleActionForAppInfo"的action,入参为"null"
  Then 执行名称为"FunctionToggleActionForAppInfo"的action失败,错误码:"900003",错误信息:"参数错误"
```

## 测试执行流程

### 1. 测试准备
```mermaid
graph TD
    A[开始测试] --> B[初始化PluginManager]
    B --> C[创建Action实例]
    C --> D[设置Mock数据]
    D --> E[准备测试参数]
    E --> F[执行测试]
```

### 2. 测试执行
```mermaid
sequenceDiagram
    participant Test as 测试用例
    participant Step as 测试步骤
    participant Action as Action
    participant Mock as Mock对象
    
    Test->>Step: Given 初始化
    Step->>Mock: 设置Mock数据
    Test->>Step: When 执行动作
    Step->>Action: 调用execute方法
    Action->>Mock: 调用业务方法
    Mock-->>Action: 返回Mock数据
    Action-->>Step: 返回结果
    Test->>Step: Then 验证结果
    Step->>Step: 断言验证
```

## 数据驱动测试

### 参数化测试优势
- **数据分离**: 测试逻辑与测试数据分离
- **批量测试**: 一个场景测试多组数据
- **维护性**: 易于添加新的测试数据
- **可读性**: 清晰的数据表格展示

### 测试数据管理
```gherkin
Examples:
  | 参数名1 | 参数名2 | 期望结果 |
  | 值1    | 值2    | 结果1   |
  | 值3    | 值4    | 结果2   |
```

## 测试标签策略

### 平台标签
- `@android_ignore`: iOS 专用功能
- `@ios_ignore`: Android 专用功能

### 测试类型标签
- `@smoke`: 冒烟测试，核心功能验证
- `@regression`: 回归测试，全面功能验证
- `@performance`: 性能测试
- `@integration`: 集成测试

### 功能模块标签
- `@device_info`: 设备信息相关
- `@app_info`: 应用信息相关
- `@cache`: 缓存管理相关
- `@gray_mode`: 灰度模式相关

## 最佳实践

### 1. 场景设计原则
- 每个场景测试一个具体功能
- 使用描述性的场景名称
- 包含正常和异常流程
- 保持场景的独立性

### 2. 数据设计原则
- 使用真实的测试数据
- 覆盖边界条件
- 包含各种数据类型
- 考虑数据的可维护性

### 3. 断言设计原则
- 验证关键字段
- 检查数据类型
- 验证业务逻辑
- 提供清晰的错误信息

## 持续改进

### 测试覆盖率监控
- 功能覆盖率：确保所有功能都有测试
- 场景覆盖率：确保各种使用场景都被测试
- 数据覆盖率：确保各种数据情况都被考虑

### 测试维护
- 定期更新测试用例
- 清理过时的测试数据
- 优化测试执行效率
- 改进测试可读性
