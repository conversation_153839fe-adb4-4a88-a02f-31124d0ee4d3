# Dependencies 依赖管理文档

## 概述

UpAppinfoPlugin 使用 CocoaPods 进行依赖管理，通过 Podfile 配置所有的外部依赖。项目依赖包括 U+ 框架组件、测试框架和第三方库。

## Podfile 配置

### 基本配置
```ruby
# Podfile
platform :ios, '10.0'
use_frameworks!

# 禁用输入输出文件列表
install! 'cocoapods', :disable_input_output_paths => true
```

### 主要目标配置
```ruby
target 'UpAppinfoPlugin' do
  # 核心依赖
  pod 'UpPluginFoundation', '~> 1.0.0'
  pod 'UPCore', '~> 2.0.0'
  pod 'UPCache', '~> 1.0.0'
  pod 'UPStorage', '~> 1.0.0'
  pod 'upnetwork', '~> 1.0.0'
  
  # 测试目标
  target 'UpAppinfoPluginTests' do
    inherit! :search_paths
    pod 'Cucumberish', '~> 1.0.0'
    pod 'OCMock', '~> 3.0.0'
    pod 'OCHamcrest', '~> 9.0.0'
  end
end
```

## 核心依赖分析

### 1. UpPluginFoundation
**版本**: ~> 1.0.0  
**用途**: U+ 插件基础框架  
**功能**:
- 提供插件基类 `UpPluginAction`
- 插件管理器 `UpPluginActionManager`
- 回调协议 `UPPCallBackProtocol`
- 通用结果类 `UPCommonResult`

**关键类**:
```objective-c
@interface UpPluginAction : NSObject <UpPluginActionProtocol>
@interface UpPluginActionManager : NSObject
@protocol UPPCallBackProtocol <NSObject>
@interface UPCommonResult : NSObject
```

### 2. UPCore
**版本**: ~> 2.0.0  
**用途**: U+ 核心框架  
**功能**:
- 应用上下文管理 `UPContext`
- 功能开关管理 `UPFunctionToggle`
- 配置管理和环境切换

**关键类**:
```objective-c
@interface UPContext : NSObject
@property (nonatomic, strong) NSString *appVersion;
@property (nonatomic, assign) BOOL isPreviewEnv;

@interface UPFunctionToggle : NSObject
+ (instancetype)shareInstance;
- (id)toggleValueForKey:(NSString *)key;
```

### 3. UPCache
**版本**: ~> 1.0.0  
**用途**: 缓存管理框架  
**功能**:
- 缓存大小计算
- 缓存清理工具
- 异步缓存操作

**关键类**:
```objective-c
@interface CHaierCleanCacheTool : NSObject
+ (void)cleanCacheWithCallback:(id<UPResCacheCleanCallback>)callback;
+ (unsigned long long)getCacheSize;
```

### 4. UPStorage
**版本**: ~> 1.0.0  
**用途**: 存储管理框架  
**功能**:
- 键值对存储
- 配置持久化
- 数据加密存储

**关键方法**:
```objective-c
+ (NSString *)getStringValue:(NSString *)key defaultValue:(NSString *)defaultValue;
+ (void)setStringValue:(NSString *)value forKey:(NSString *)key;
+ (BOOL)getBoolValue:(NSString *)key defaultValue:(BOOL)defaultValue;
```

### 5. upnetwork
**版本**: ~> 1.0.0  
**用途**: 网络配置框架  
**功能**:
- 网络配置管理
- API 密钥管理
- 客户端标识管理

**关键类**:
```objective-c
@interface UPNetworkSettings : NSObject
@property (nonatomic, strong) NSString *appID;
@property (nonatomic, strong) NSString *appKey;
@property (nonatomic, strong) NSString *clientID;
```

## 测试依赖分析

### 1. Cucumberish
**版本**: ~> 1.0.0  
**用途**: BDD 测试框架  
**功能**:
- Gherkin 语法支持
- 场景驱动测试
- 步骤定义管理

**使用示例**:
```objective-c
#import <Cucumberish/Cucumberish.h>

Given(@"^UpAppInfoPluginManager已经初始化$", ^(NSArray<NSString *> *args, NSDictionary *userInfo) {
    // 测试步骤实现
});
```

### 2. OCMock
**版本**: ~> 3.0.0  
**用途**: Objective-C Mock 框架  
**功能**:
- 对象模拟
- 方法存根
- 调用验证

**使用示例**:
```objective-c
#import <OCMock/OCMock.h>

// 创建 Mock 对象
id mockDelegate = OCMClassMock([UpAppinfoPluginIMP class]);

// 设置方法返回值
OCMStub([mockDelegate getDeviceIdentifier]).andReturn(@"iPhone13,2");

// 验证方法调用
OCMVerify([mockDelegate getDeviceIdentifier]);
```

### 3. OCHamcrest
**版本**: ~> 9.0.0  
**用途**: 匹配器框架  
**功能**:
- 丰富的断言匹配器
- 可读性强的测试断言
- 自定义匹配器支持

**使用示例**:
```objective-c
#import <OCHamcrest/OCHamcrest.h>

// 断言示例
assertThat(result, is(equalTo(@"expected")));
assertThat(array, hasCountOf(3));
assertThat(dictionary, hasKey(@"key"));
```

## 依赖关系图

```mermaid
graph TD
    subgraph "UpAppinfoPlugin"
        Plugin[UpAppinfoPlugin]
        Manager[UpAppinfoPluginManager]
        Actions[Actions]
        Framework[UpAppinfoPluginFramework]
    end
    
    subgraph "U+ 框架依赖"
        Foundation[UpPluginFoundation]
        Core[UPCore]
        Cache[UPCache]
        Storage[UPStorage]
        Network[upnetwork]
    end
    
    subgraph "测试依赖"
        Cucumber[Cucumberish]
        OCMock[OCMock]
        Hamcrest[OCHamcrest]
    end
    
    subgraph "系统框架"
        UIKit[UIKit]
        Foundation_iOS[Foundation]
    end
    
    Plugin --> Foundation
    Manager --> Foundation
    Actions --> Foundation
    Framework --> Core
    Framework --> Cache
    Framework --> Storage
    Framework --> Network
    Framework --> UIKit
    Framework --> Foundation_iOS
    
    Plugin -.-> Cucumber
    Plugin -.-> OCMock
    Plugin -.-> Hamcrest
```

## 版本兼容性

### iOS 版本支持
- **最低支持版本**: iOS 10.0
- **推荐版本**: iOS 12.0+
- **测试覆盖**: iOS 10.0 - iOS 15.0

### 依赖版本策略
```ruby
# 使用悲观版本控制
pod 'UpPluginFoundation', '~> 1.0.0'  # >= 1.0.0, < 1.1.0
pod 'UPCore', '~> 2.0.0'              # >= 2.0.0, < 2.1.0

# 精确版本控制（如需要）
pod 'SpecificLibrary', '1.2.3'

# 最低版本要求
pod 'FlexibleLibrary', '>= 1.0.0'
```

## 依赖管理最佳实践

### 1. 版本管理
- 使用语义化版本控制
- 定期更新依赖版本
- 测试新版本兼容性
- 记录版本变更影响

### 2. 依赖隔离
```ruby
# 测试依赖只在测试目标中引入
target 'UpAppinfoPluginTests' do
  inherit! :search_paths
  # 测试专用依赖
end
```

### 3. 性能优化
```ruby
# 禁用不必要的功能
install! 'cocoapods', :disable_input_output_paths => true

# 使用静态库（如适用）
use_frameworks! :linkage => :static
```

## 依赖安装和更新

### 初始安装
```bash
# 安装依赖
pod install

# 更新依赖
pod update

# 安装特定版本
pod install --repo-update
```

### 依赖检查
```bash
# 检查过时的依赖
pod outdated

# 验证 Podfile
pod spec lint

# 清理缓存
pod cache clean --all
```

## 故障排除

### 常见问题

#### 1. 依赖冲突
```bash
# 清理并重新安装
rm -rf Pods/
rm Podfile.lock
pod install
```

#### 2. 版本不兼容
```ruby
# 在 Podfile 中指定兼容版本
pod 'ConflictingLibrary', '~> 1.0.0'
```

#### 3. 编译错误
```bash
# 清理构建缓存
xcodebuild clean
rm -rf ~/Library/Developer/Xcode/DerivedData/
```

### 调试技巧
- 检查 Podfile.lock 文件
- 查看依赖的传递依赖
- 使用 `pod env` 检查环境
- 查看详细的安装日志

## 安全考虑

### 1. 依赖审计
- 定期检查依赖的安全漏洞
- 使用可信的依赖源
- 避免使用过时的依赖版本

### 2. 私有依赖
```ruby
# 使用私有 Pod 仓库
source 'https://private-repo.company.com/specs.git'
source 'https://github.com/CocoaPods/Specs.git'
```

### 3. 依赖锁定
```ruby
# 使用 Podfile.lock 锁定版本
# 确保团队使用相同的依赖版本
```

## 未来规划

### 依赖升级计划
- 定期评估新版本的功能和性能改进
- 制定渐进式升级策略
- 建立回滚机制

### 依赖优化
- 减少不必要的依赖
- 考虑依赖的替代方案
- 优化依赖的加载性能

### 模块化改进
- 将大型依赖拆分为小模块
- 实现按需加载
- 提高代码复用性
